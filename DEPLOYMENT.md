# Deployment Guide

This guide will help you deploy your EliteStore e-commerce platform to production.

## 🚀 Quick Deployment (Recommended)

### Vercel Deployment

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Connect your GitHub repository
   - Import your project
   - Set environment variables (see below)
   - Deploy!

3. **Environment Variables**
   Add these in Vercel dashboard:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   NEXT_PUBLIC_SITE_URL=https://your-domain.vercel.app
   ```

## 🐳 Docker Deployment

### Build and Run
```bash
# Build the image
docker build -t elitestore .

# Run the container
docker run -p 3000:3000 elitestore
```

### Docker Compose
```bash
# Development
docker-compose --profile dev up

# Production
docker-compose --profile production up
```

## ⚙️ GitHub Actions Setup (Optional)

To enable the CI/CD pipeline, uncomment the deployment jobs in `.github/workflows/ci.yml` and add these secrets to your GitHub repository:

### GitHub Secrets
Go to your repository → Settings → Secrets and variables → Actions

**For Vercel Deployment:**
- `VERCEL_TOKEN` - Your Vercel token
- `VERCEL_ORG_ID` - Your Vercel organization ID
- `VERCEL_PROJECT_ID` - Your Vercel project ID

**For Lighthouse CI:**
- `LHCI_GITHUB_APP_TOKEN` - Lighthouse CI GitHub app token

### How to Get Vercel Secrets

1. **Vercel Token**
   - Go to Vercel → Settings → Tokens
   - Create a new token

2. **Organization and Project IDs**
   ```bash
   npx vercel link
   cat .vercel/project.json
   ```

## 🌐 Custom Domain Setup

### Vercel
1. Go to your project dashboard
2. Click "Domains"
3. Add your custom domain
4. Update DNS records as instructed

### Cloudflare (Optional)
For additional performance and security:
1. Add your domain to Cloudflare
2. Update nameservers
3. Enable proxy for your domain

## 📊 Monitoring Setup

### Performance Monitoring
- **Vercel Analytics** - Built-in performance monitoring
- **Google Analytics** - Add your GA4 tracking ID
- **Lighthouse CI** - Automated performance testing

### Error Tracking
- **Sentry** - Add Sentry DSN to environment variables
- **LogRocket** - Session replay and error tracking

## 🔒 Security Checklist

- [ ] Environment variables are set correctly
- [ ] Database access is restricted
- [ ] HTTPS is enabled
- [ ] Security headers are configured
- [ ] Rate limiting is implemented
- [ ] Input validation is in place

## 🚀 Production Optimizations

### Performance
- [ ] Images are optimized (WebP/AVIF)
- [ ] Caching is configured
- [ ] CDN is set up
- [ ] Bundle size is optimized

### SEO
- [ ] Sitemap is generated
- [ ] Meta tags are configured
- [ ] Structured data is implemented
- [ ] Analytics are set up

## 📱 Mobile App (Future)

Consider these options for mobile apps:
- **React Native** - Share code with web
- **Capacitor** - Web-to-mobile wrapper
- **PWA** - Progressive Web App (already configured)

## 🆘 Troubleshooting

### Common Issues

**Build Errors**
- Check Node.js version (18+)
- Clear `.next` folder and rebuild
- Check environment variables

**Database Connection**
- Verify Supabase credentials
- Check network connectivity
- Review database permissions

**Performance Issues**
- Enable caching
- Optimize images
- Check bundle size

### Getting Help

1. Check the [README.md](README.md) for basic setup
2. Review error logs in Vercel dashboard
3. Check browser console for client-side errors
4. Contact support if needed

## 📞 Support

For deployment support:
- Email: <EMAIL>
- Documentation: [docs.elitestore.com](https://docs.elitestore.com)
- Community: [Discord](https://discord.gg/elitestore)

---

**Happy Deploying! 🚀**
