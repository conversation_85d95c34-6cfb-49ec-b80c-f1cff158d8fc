{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/app/layout.tsx"], "sourcesContent": ["export default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return children\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}