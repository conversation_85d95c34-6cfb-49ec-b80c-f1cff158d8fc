import type { Metadata } from "next";
import { CartProvider } from "@/contexts/CartContext";
import { CartSidebar } from "@/components/cart/CartSidebar";
import { PerformanceMonitor } from "@/components/analytics/PerformanceMonitor";
import { StructuredData, generateOrganizationStructuredData, generateWebsiteStructuredData } from "@/lib/seo";
import { notFound } from 'next/navigation';

export function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'tr' },
    { locale: 'de' }
  ];
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  const title = "EliteStore - Premium Fashion Collection";
  const description = "Discover our exclusive collection of premium clothing designed for the modern lifestyle.";

  return {
    title,
    description,
    keywords: ["fashion", "clothing", "premium", "style", "modern", "quality"],
    authors: [{ name: "EliteStore" }],
    creator: "EliteStore",
    publisher: "EliteStore",
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL("https://elitestore.com"),
    alternates: {
      canonical: `/${locale}`,
      languages: {
        "en-US": "/en",
        "tr-TR": "/tr",
        "de-DE": "/de",
      },
    },
    openGraph: {
      title,
      description,
      url: `https://elitestore.com/${locale}`,
      siteName: "EliteStore",
      images: [
        {
          url: "/og-image.jpg",
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/og-image.jpg"],
      creator: "@elitestore",
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  const validLocales = ['en', 'tr', 'de'];
  if (!validLocales.includes(locale)) {
    notFound();
  }

  return (
    <div>
      {/* Structured Data */}
      <StructuredData data={generateOrganizationStructuredData()} />
      <StructuredData data={generateWebsiteStructuredData()} />

      <CartProvider>
        {children}
        <CartSidebar />
      </CartProvider>

      {/* Performance Monitoring */}
      <PerformanceMonitor />
    </div>
  );
}
