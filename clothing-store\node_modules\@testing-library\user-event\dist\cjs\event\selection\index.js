'use strict';

var getInputRange = require('./getInputRange.js');
var modifySelection = require('./modifySelection.js');
var moveSelection = require('./moveSelection.js');
var setSelectionPerMouse = require('./setSelectionPerMouse.js');
var modifySelectionPerMouse = require('./modifySelectionPerMouse.js');
var selectAll = require('./selectAll.js');
var setSelectionRange = require('./setSelectionRange.js');
var setSelection = require('./setSelection.js');
var updateSelectionOnFocus = require('./updateSelectionOnFocus.js');



exports.getInputRange = getInputRange.getInputRange;
exports.modifySelection = modifySelection.modifySelection;
exports.moveSelection = moveSelection.moveSelection;
exports.setSelectionPerMouseDown = setSelectionPerMouse.setSelectionPerMouseDown;
exports.modifySelectionPerMouseMove = modifySelectionPerMouse.modifySelectionPerMouseMove;
exports.isAllSelected = selectAll.isAllSelected;
exports.selectAll = selectAll.selectAll;
exports.setSelectionRange = setSelectionRange.setSelectionRange;
exports.setSelection = setSelection.setSelection;
exports.updateSelectionOnFocus = updateSelectionOnFocus.updateSelectionOnFocus;
