// Product Types
export interface Product {
  id: string
  name: string
  description: string
  price: number
  salePrice?: number
  sku: string
  slug: string
  images: ProductImage[]
  category: Category
  subcategory?: Subcategory
  variants: ProductVariant[]
  tags: string[]
  inStock: boolean
  stockQuantity: number
  featured: boolean
  createdAt: string
  updatedAt: string
  seoTitle?: string
  seoDescription?: string
  weight?: number
  dimensions?: ProductDimensions
}

export interface ProductImage {
  id: string
  url: string
  alt: string
  isPrimary: boolean
  order: number
}

export interface ProductVariant {
  id: string
  name: string
  type: 'size' | 'color' | 'material' | 'style'
  value: string
  price?: number
  sku?: string
  stockQuantity: number
  image?: string
}

export interface ProductDimensions {
  length: number
  width: number
  height: number
  unit: 'cm' | 'in'
}

// Category Types
export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  parentId?: string
  order: number
  isActive: boolean
  seoTitle?: string
  seoDescription?: string
}

export interface Subcategory {
  id: string
  name: string
  slug: string
  description?: string
  categoryId: string
  order: number
  isActive: boolean
}

// Cart Types
export interface CartItem {
  id: string
  productId: string
  product: Product
  variantId?: string
  variant?: ProductVariant
  quantity: number
  price: number
  addedAt: string
}

export interface Cart {
  id: string
  items: CartItem[]
  subtotal: number
  tax: number
  shipping: number
  total: number
  currency: string
  updatedAt: string
}

// Order Types
export interface Order {
  id: string
  orderNumber: string
  customerId?: string
  customer: Customer
  items: OrderItem[]
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  currency: string
  status: OrderStatus
  paymentStatus: PaymentStatus
  shippingAddress: Address
  billingAddress: Address
  paymentMethod: PaymentMethod
  shippingMethod: ShippingMethod
  notes?: string
  createdAt: string
  updatedAt: string
  shippedAt?: string
  deliveredAt?: string
}

export interface OrderItem {
  id: string
  productId: string
  product: Product
  variantId?: string
  variant?: ProductVariant
  quantity: number
  price: number
  total: number
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded'

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded'
  | 'partially_refunded'

// Customer Types
export interface Customer {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  dateOfBirth?: string
  addresses: Address[]
  orders: Order[]
  createdAt: string
  updatedAt: string
  isActive: boolean
  preferences: CustomerPreferences
}

export interface CustomerPreferences {
  language: string
  currency: string
  newsletter: boolean
  smsNotifications: boolean
  emailNotifications: boolean
}

// Address Types
export interface Address {
  id: string
  type: 'shipping' | 'billing'
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  state: string
  postalCode: string
  country: string
  phone?: string
  isDefault: boolean
}

// Payment Types
export interface PaymentMethod {
  id: string
  type: 'credit_card' | 'debit_card' | 'paypal' | 'stripe' | 'bank_transfer'
  provider: string
  last4?: string
  expiryMonth?: number
  expiryYear?: number
  brand?: string
  isDefault: boolean
}

// Shipping Types
export interface ShippingMethod {
  id: string
  name: string
  description: string
  price: number
  estimatedDays: number
  isActive: boolean
}

// User Types
export interface User {
  id: string
  email: string
  role: UserRole
  profile: UserProfile
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  isActive: boolean
}

export type UserRole = 'admin' | 'manager' | 'customer'

export interface UserProfile {
  firstName: string
  lastName: string
  avatar?: string
  phone?: string
  language: string
  timezone: string
}

// Search & Filter Types
export interface SearchFilters {
  query?: string
  category?: string
  subcategory?: string
  minPrice?: number
  maxPrice?: number
  inStock?: boolean
  featured?: boolean
  tags?: string[]
  sortBy?: 'name' | 'price' | 'created' | 'popularity'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface SearchResult<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form Types
export interface ContactForm {
  name: string
  email: string
  subject: string
  message: string
}

export interface NewsletterForm {
  email: string
  firstName?: string
  preferences?: string[]
}

// Language & Localization Types
export type SupportedLanguage = 'en' | 'tr' | 'de'

export interface LocalizedContent {
  en: string
  tr: string
  de: string
}

// Admin Types
export interface AdminStats {
  totalProducts: number
  totalOrders: number
  totalCustomers: number
  totalRevenue: number
  recentOrders: Order[]
  topProducts: Product[]
  salesData: SalesData[]
}

export interface SalesData {
  date: string
  sales: number
  orders: number
}

// Component Props Types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
}

export interface InputProps extends BaseComponentProps {
  type?: string
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  disabled?: boolean
  error?: string
  label?: string
  required?: boolean
}
