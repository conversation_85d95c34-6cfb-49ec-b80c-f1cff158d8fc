'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ProductGridWithFilters } from '@/components/products/ProductGrid'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { useCart } from '@/contexts/CartContext'
import { Search, Filter, X } from 'lucide-react'
import { Product, Category } from '@/types'

// Mock data for demonstration
const mockCategories: Category[] = [
  { id: '1', name: "Men's Clothing", slug: 'mens-clothing', description: '', order: 1, isActive: true },
  { id: '2', name: "Women's Clothing", slug: 'womens-clothing', description: '', order: 2, isActive: true },
  { id: '3', name: 'Accessories', slug: 'accessories', description: '', order: 3, isActive: true },
  { id: '4', name: 'Sale', slug: 'sale', description: '', order: 4, isActive: true },
]

const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Premium Cotton T-Shirt',
    description: 'High-quality cotton t-shirt with perfect fit and comfort.',
    price: 49.99,
    salePrice: undefined,
    sku: 'MEN-TSHIRT-001',
    slug: 'premium-cotton-tshirt',
    images: [
      { id: '1', url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500', alt: 'Premium Cotton T-Shirt', isPrimary: true, order: 1 }
    ],
    category: mockCategories[0],
    variants: [
      { id: '1', name: 'Small', type: 'size', value: 'S', stockQuantity: 15 },
      { id: '2', name: 'Medium', type: 'size', value: 'M', stockQuantity: 20 },
      { id: '3', name: 'Large', type: 'size', value: 'L', stockQuantity: 15 },
    ],
    tags: ['cotton', 'casual', 'comfortable'],
    inStock: true,
    stockQuantity: 50,
    featured: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    name: 'Classic Denim Jeans',
    description: 'Timeless denim jeans crafted from premium denim fabric.',
    price: 89.99,
    salePrice: 69.99,
    sku: 'MEN-JEANS-001',
    slug: 'classic-denim-jeans',
    images: [
      { id: '2', url: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500', alt: 'Classic Denim Jeans', isPrimary: true, order: 1 }
    ],
    category: mockCategories[0],
    variants: [
      { id: '4', name: '30', type: 'size', value: '30', stockQuantity: 8 },
      { id: '5', name: '32', type: 'size', value: '32', stockQuantity: 12 },
      { id: '6', name: '34', type: 'size', value: '34', stockQuantity: 10 },
    ],
    tags: ['denim', 'classic', 'versatile'],
    inStock: true,
    stockQuantity: 30,
    featured: true,
    createdAt: '2024-01-02',
    updatedAt: '2024-01-02',
  },
  {
    id: '3',
    name: 'Elegant Silk Blouse',
    description: 'Beautiful silk blouse with elegant draping and sophisticated design.',
    price: 119.99,
    salePrice: 99.99,
    sku: 'WOM-BLOUSE-001',
    slug: 'elegant-silk-blouse',
    images: [
      { id: '3', url: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500', alt: 'Elegant Silk Blouse', isPrimary: true, order: 1 }
    ],
    category: mockCategories[1],
    variants: [
      { id: '7', name: 'XS', type: 'size', value: 'XS', stockQuantity: 5 },
      { id: '8', name: 'S', type: 'size', value: 'S', stockQuantity: 8 },
      { id: '9', name: 'M', type: 'size', value: 'M', stockQuantity: 7 },
    ],
    tags: ['silk', 'elegant', 'professional'],
    inStock: true,
    stockQuantity: 20,
    featured: true,
    createdAt: '2024-01-03',
    updatedAt: '2024-01-03',
  },
  {
    id: '4',
    name: 'Leather Crossbody Bag',
    description: 'Stylish leather crossbody bag with multiple compartments.',
    price: 149.99,
    salePrice: 129.99,
    sku: 'ACC-BAG-001',
    slug: 'leather-crossbody-bag',
    images: [
      { id: '4', url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=500', alt: 'Leather Crossbody Bag', isPrimary: true, order: 1 }
    ],
    category: mockCategories[2],
    variants: [
      { id: '10', name: 'Black', type: 'color', value: 'Black', stockQuantity: 10 },
      { id: '11', name: 'Brown', type: 'color', value: 'Brown', stockQuantity: 8 },
    ],
    tags: ['leather', 'bag', 'crossbody'],
    inStock: true,
    stockQuantity: 18,
    featured: true,
    createdAt: '2024-01-04',
    updatedAt: '2024-01-04',
  },
]

export default function ProductsPage() {
  const { addItem } = useCart()
  const [products, setProducts] = useState<Product[]>(mockProducts)
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(mockProducts)
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 500])
  const [showOnlyInStock, setShowOnlyInStock] = useState(false)
  const [showOnlyFeatured, setShowOnlyFeatured] = useState(false)
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'created' | 'popularity'>('popularity')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showFilters, setShowFilters] = useState(false)

  // Filter and sort products
  useEffect(() => {
    let filtered = [...products]

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Category filter
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category.id === selectedCategory)
    }

    // Price range filter
    filtered = filtered.filter(product => {
      const price = product.salePrice || product.price
      return price >= priceRange[0] && price <= priceRange[1]
    })

    // Stock filter
    if (showOnlyInStock) {
      filtered = filtered.filter(product => product.inStock)
    }

    // Featured filter
    if (showOnlyFeatured) {
      filtered = filtered.filter(product => product.featured)
    }

    // Sort products
    filtered.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortBy) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'price':
          aValue = a.salePrice || a.price
          bValue = b.salePrice || b.price
          break
        case 'created':
          aValue = new Date(a.createdAt)
          bValue = new Date(b.createdAt)
          break
        default:
          aValue = a.featured ? 1 : 0
          bValue = b.featured ? 1 : 0
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredProducts(filtered)
  }, [products, searchQuery, selectedCategory, priceRange, showOnlyInStock, showOnlyFeatured, sortBy, sortOrder])

  const clearFilters = () => {
    setSearchQuery('')
    setSelectedCategory('')
    setPriceRange([0, 500])
    setShowOnlyInStock(false)
    setShowOnlyFeatured(false)
    setSortBy('popularity')
    setSortOrder('desc')
  }

  const hasActiveFilters = searchQuery || selectedCategory || showOnlyInStock || showOnlyFeatured || 
    priceRange[0] > 0 || priceRange[1] < 500

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-50 to-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold text-primary mb-4">Our Products</h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our complete collection of premium clothing and accessories
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="lg:sticky lg:top-4">
              {/* Mobile Filter Toggle */}
              <div className="lg:hidden mb-4">
                <Button
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  className="w-full"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                  {hasActiveFilters && (
                    <span className="ml-2 bg-accent text-primary text-xs px-2 py-1 rounded-full">
                      Active
                    </span>
                  )}
                </Button>
              </div>

              {/* Filters */}
              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center justify-between">
                      Filters
                      {hasActiveFilters && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={clearFilters}
                          className="text-xs"
                        >
                          Clear All
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Search */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Search</label>
                      <Input
                        placeholder="Search products..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        leftIcon={<Search className="h-4 w-4" />}
                      />
                    </div>

                    {/* Categories */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Category</label>
                      <select
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                      >
                        <option value="">All Categories</option>
                        {mockCategories.map(category => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Price Range */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Price Range: ${priceRange[0]} - ${priceRange[1]}
                      </label>
                      <div className="space-y-2">
                        <input
                          type="range"
                          min="0"
                          max="500"
                          value={priceRange[0]}
                          onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                          className="w-full"
                        />
                        <input
                          type="range"
                          min="0"
                          max="500"
                          value={priceRange[1]}
                          onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                          className="w-full"
                        />
                      </div>
                    </div>

                    {/* Checkboxes */}
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={showOnlyInStock}
                          onChange={(e) => setShowOnlyInStock(e.target.checked)}
                          className="mr-2"
                        />
                        <span className="text-sm">In Stock Only</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={showOnlyFeatured}
                          onChange={(e) => setShowOnlyFeatured(e.target.checked)}
                          className="mr-2"
                        />
                        <span className="text-sm">Featured Only</span>
                      </label>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <ProductGridWithFilters
              products={filteredProducts}
              loading={loading}
              showFilters={true}
              filters={{
                sortBy,
                sortOrder,
              }}
              onFiltersChange={(filters) => {
                setSortBy(filters.sortBy)
                setSortOrder(filters.sortOrder)
              }}
              onQuickView={(product) => {
                console.log('Quick view:', product)
              }}
              onAddToCart={(product) => {
                addItem(product)
              }}
              onToggleWishlist={(product) => {
                console.log('Toggle wishlist:', product)
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
