{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'elevated' | 'outlined' | 'ghost'\n  padding?: 'none' | 'sm' | 'md' | 'lg'\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'default', padding = 'md', children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-lg border bg-card text-card-foreground transition-all duration-200',\n          {\n            'border-border shadow-sm hover:shadow-md': variant === 'default',\n            'border-border shadow-lg hover:shadow-xl': variant === 'elevated',\n            'border-2 border-border shadow-none': variant === 'outlined',\n            'border-none shadow-none bg-transparent': variant === 'ghost',\n          },\n          {\n            'p-0': padding === 'none',\n            'p-3': padding === 'sm',\n            'p-6': padding === 'md',\n            'p-8': padding === 'lg',\n          },\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\nCard.displayName = 'Card'\n\nconst CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 pb-6', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, children, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-2xl font-semibold leading-none tracking-tight text-foreground', className)}\n      {...props}\n    >\n      {children}\n    </h3>\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center pt-6', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAOA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,UAAU,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvE,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8EACA;YACE,2CAA2C,YAAY;YACvD,2CAA2C,YAAY;YACvD,sCAAsC,YAAY;YAClD,0CAA0C,YAAY;QACxD,GACA;YACE,OAAO,YAAY;YACnB,OAAO,YAAY;YACnB,OAAO,YAAY;YACnB,OAAO,YAAY;QACrB,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEF,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAC/C,GAAG,KAAK;;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;QACnF,GAAG,KAAK;kBAER;;;;;;;AAIP,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;;AAG9D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/products/ProductCard.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { motion } from 'framer-motion'\nimport { Heart, ShoppingCart, Star, Eye } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { cn, formatPrice, calculateDiscountPercentage } from '@/lib/utils'\nimport { Product } from '@/types'\n\ninterface ProductCardProps {\n  product: Product\n  className?: string\n  showQuickView?: boolean\n  showAddToCart?: boolean\n  showWishlist?: boolean\n  onQuickView?: (product: Product) => void\n  onAddToCart?: (product: Product) => void\n  onToggleWishlist?: (product: Product) => void\n  isInWishlist?: boolean\n}\n\nexport function ProductCard({\n  product,\n  className,\n  showQuickView = true,\n  showAddToCart = true,\n  showWishlist = true,\n  onQuickView,\n  onAddToCart,\n  onToggleWishlist,\n  isInWishlist = false,\n}: ProductCardProps) {\n  const primaryImage = product.images?.find(img => img.isPrimary) || product.images?.[0]\n  const hasDiscount = product.salePrice && product.salePrice < product.price\n  const discountPercentage = hasDiscount ? calculateDiscountPercentage(product.price, product.salePrice!) : 0\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      viewport={{ once: true }}\n      className={cn('group', className)}\n    >\n      <Card className=\"overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300\">\n        <CardContent className=\"p-0\">\n          {/* Image Container */}\n          <div className=\"relative aspect-square overflow-hidden bg-gray-100\">\n            {primaryImage ? (\n              <Image\n                src={primaryImage.url}\n                alt={primaryImage.alt || product.name}\n                fill\n                className=\"object-cover transition-transform duration-300 group-hover:scale-105\"\n                sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n              />\n            ) : (\n              <div className=\"flex items-center justify-center h-full bg-gray-200\">\n                <ShoppingCart className=\"h-16 w-16 text-gray-400\" />\n              </div>\n            )}\n\n            {/* Discount Badge */}\n            {hasDiscount && (\n              <div className=\"absolute top-3 left-3 bg-accent text-primary px-2 py-1 rounded-md text-sm font-semibold\">\n                -{discountPercentage}%\n              </div>\n            )}\n\n            {/* Featured Badge */}\n            {product.featured && (\n              <div className=\"absolute top-3 right-3 bg-primary text-secondary px-2 py-1 rounded-md text-sm font-semibold\">\n                Featured\n              </div>\n            )}\n\n            {/* Wishlist Button */}\n            {showWishlist && (\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className={cn(\n                  \"absolute top-3 right-3 bg-white/80 hover:bg-white opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                  product.featured && \"top-12\"\n                )}\n                onClick={(e) => {\n                  e.preventDefault()\n                  onToggleWishlist?.(product)\n                }}\n              >\n                <Heart\n                  className={cn(\n                    \"h-4 w-4\",\n                    isInWishlist ? \"fill-red-500 text-red-500\" : \"text-gray-600\"\n                  )}\n                />\n              </Button>\n            )}\n\n            {/* Overlay Actions */}\n            <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n              <div className=\"flex gap-2\">\n                {showQuickView && (\n                  <Button\n                    size=\"sm\"\n                    variant=\"secondary\"\n                    onClick={(e) => {\n                      e.preventDefault()\n                      onQuickView?.(product)\n                    }}\n                  >\n                    <Eye className=\"h-4 w-4 mr-1\" />\n                    Quick View\n                  </Button>\n                )}\n                {showAddToCart && (\n                  <Button\n                    size=\"sm\"\n                    onClick={(e) => {\n                      e.preventDefault()\n                      onAddToCart?.(product)\n                    }}\n                  >\n                    <ShoppingCart className=\"h-4 w-4 mr-1\" />\n                    Add to Cart\n                  </Button>\n                )}\n              </div>\n            </div>\n\n            {/* Stock Status */}\n            {!product.inStock && (\n              <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\n                <span className=\"bg-white text-black px-4 py-2 rounded-md font-semibold\">\n                  Out of Stock\n                </span>\n              </div>\n            )}\n          </div>\n\n          {/* Product Info */}\n          <div className=\"p-4\">\n            <Link href={`/products/${product.slug}`} className=\"block\">\n              <h3 className=\"font-semibold text-primary mb-1 hover:text-accent transition-colors line-clamp-2\">\n                {product.name}\n              </h3>\n            </Link>\n\n            {/* Category */}\n            <p className=\"text-sm text-muted-foreground mb-2\">\n              {product.category.name}\n            </p>\n\n            {/* Rating */}\n            <div className=\"flex items-center mb-2\">\n              {[...Array(5)].map((_, i) => (\n                <Star\n                  key={i}\n                  className=\"h-4 w-4 fill-accent text-accent\"\n                />\n              ))}\n              <span className=\"text-sm text-muted-foreground ml-1\">(4.8)</span>\n            </div>\n\n            {/* Price */}\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                {hasDiscount ? (\n                  <>\n                    <span className=\"text-lg font-bold text-primary\">\n                      {formatPrice(product.salePrice!)}\n                    </span>\n                    <span className=\"text-sm text-muted-foreground line-through\">\n                      {formatPrice(product.price)}\n                    </span>\n                  </>\n                ) : (\n                  <span className=\"text-lg font-bold text-primary\">\n                    {formatPrice(product.price)}\n                  </span>\n                )}\n              </div>\n\n              {/* Stock Indicator */}\n              <div className=\"flex items-center\">\n                <div\n                  className={cn(\n                    \"w-2 h-2 rounded-full mr-1\",\n                    product.inStock ? \"bg-green-500\" : \"bg-red-500\"\n                  )}\n                />\n                <span className=\"text-xs text-muted-foreground\">\n                  {product.inStock ? 'In Stock' : 'Out of Stock'}\n                </span>\n              </div>\n            </div>\n\n            {/* Variants Preview */}\n            {product.variants && product.variants.length > 0 && (\n              <div className=\"mt-3\">\n                <div className=\"flex items-center gap-1\">\n                  <span className=\"text-xs text-muted-foreground\">Colors:</span>\n                  <div className=\"flex gap-1\">\n                    {product.variants\n                      .filter(v => v.type === 'color')\n                      .slice(0, 4)\n                      .map((variant, index) => (\n                        <div\n                          key={variant.id}\n                          className=\"w-4 h-4 rounded-full border border-gray-300\"\n                          style={{\n                            backgroundColor: variant.value.toLowerCase(),\n                          }}\n                          title={variant.value}\n                        />\n                      ))}\n                    {product.variants.filter(v => v.type === 'color').length > 4 && (\n                      <span className=\"text-xs text-muted-foreground\">\n                        +{product.variants.filter(v => v.type === 'color').length - 4}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;AAwBO,SAAS,YAAY,EAC1B,OAAO,EACP,SAAS,EACT,gBAAgB,IAAI,EACpB,gBAAgB,IAAI,EACpB,eAAe,IAAI,EACnB,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,eAAe,KAAK,EACH;IACjB,MAAM,eAAe,QAAQ,MAAM,EAAE,KAAK,CAAA,MAAO,IAAI,SAAS,KAAK,QAAQ,MAAM,EAAE,CAAC,EAAE;IACtF,MAAM,cAAc,QAAQ,SAAS,IAAI,QAAQ,SAAS,GAAG,QAAQ,KAAK;IAC1E,MAAM,qBAAqB,cAAc,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD,EAAE,QAAQ,KAAK,EAAE,QAAQ,SAAS,IAAK;IAE1G,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;QAC5B,UAAU;YAAE,MAAM;QAAK;QACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,SAAS;kBAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,aAAa,GAAG;gCACrB,KAAK,aAAa,GAAG,IAAI,QAAQ,IAAI;gCACrC,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;qDAGR,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;4BAK3B,6BACC,6LAAC;gCAAI,WAAU;;oCAA0F;oCACrG;oCAAmB;;;;;;;4BAKxB,QAAQ,QAAQ,kBACf,6LAAC;gCAAI,WAAU;0CAA8F;;;;;;4BAM9G,8BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mHACA,QAAQ,QAAQ,IAAI;gCAEtB,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,mBAAmB;gCACrB;0CAEA,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,eAAe,8BAA8B;;;;;;;;;;;0CAOrD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,+BACC,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,CAAC;gDACR,EAAE,cAAc;gDAChB,cAAc;4CAChB;;8DAEA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAInC,+BACC,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,CAAC;gDACR,EAAE,cAAc;gDAChB,cAAc;4CAChB;;8DAEA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;4BAQhD,CAAC,QAAQ,OAAO,kBACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAyD;;;;;;;;;;;;;;;;;kCAQ/E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;gCAAE,WAAU;0CACjD,cAAA,6LAAC;oCAAG,WAAU;8CACX,QAAQ,IAAI;;;;;;;;;;;0CAKjB,6LAAC;gCAAE,WAAU;0CACV,QAAQ,QAAQ,CAAC,IAAI;;;;;;0CAIxB,6LAAC;gCAAI,WAAU;;oCACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;4CAEH,WAAU;2CADL;;;;;kDAIT,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;;;;;;;0CAIvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,4BACC;;8DACE,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,SAAS;;;;;;8DAEhC,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;;yEAI9B,6LAAC;4CAAK,WAAU;sDACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;;;;;;kDAMhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,QAAQ,OAAO,GAAG,iBAAiB;;;;;;0DAGvC,6LAAC;gDAAK,WAAU;0DACb,QAAQ,OAAO,GAAG,aAAa;;;;;;;;;;;;;;;;;;4BAMrC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;sDAChD,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,QAAQ,CACd,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SACvB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,SAAS,sBACb,6LAAC;wDAEC,WAAU;wDACV,OAAO;4DACL,iBAAiB,QAAQ,KAAK,CAAC,WAAW;wDAC5C;wDACA,OAAO,QAAQ,KAAK;uDALf,QAAQ,EAAE;;;;;gDAQpB,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM,GAAG,mBACzD,6LAAC;oDAAK,WAAU;;wDAAgC;wDAC5C,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYpF;KAjNgB", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/products/ProductGrid.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { ProductCard } from './ProductCard'\nimport { Product } from '@/types'\nimport { cn } from '@/lib/utils'\n\ninterface ProductGridProps {\n  products: Product[]\n  className?: string\n  columns?: 2 | 3 | 4 | 5\n  showQuickView?: boolean\n  showAddToCart?: boolean\n  showWishlist?: boolean\n  onQuickView?: (product: Product) => void\n  onAddToCart?: (product: Product) => void\n  onToggleWishlist?: (product: Product) => void\n  wishlistItems?: string[]\n  loading?: boolean\n}\n\nexport function ProductGrid({\n  products,\n  className,\n  columns = 4,\n  showQuickView = true,\n  showAddToCart = true,\n  showWishlist = true,\n  onQuickView,\n  onAddToCart,\n  onToggleWishlist,\n  wishlistItems = [],\n  loading = false,\n}: ProductGridProps) {\n  const gridCols = {\n    2: 'grid-cols-1 sm:grid-cols-2',\n    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',\n    5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',\n  }\n\n  if (loading) {\n    return (\n      <div className={cn('grid gap-6', gridCols[columns], className)}>\n        {[...Array(8)].map((_, index) => (\n          <ProductCardSkeleton key={index} />\n        ))}\n      </div>\n    )\n  }\n\n  if (products.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg\n              className=\"w-12 h-12 text-gray-400\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1m16 0H4\"\n              />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No products found</h3>\n          <p className=\"text-gray-600\">\n            We couldn't find any products matching your criteria. Try adjusting your filters or search terms.\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={cn('grid gap-6', gridCols[columns], className)}>\n      {products.map((product, index) => (\n        <motion.div\n          key={product.id}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: index * 0.1 }}\n        >\n          <ProductCard\n            product={product}\n            showQuickView={showQuickView}\n            showAddToCart={showAddToCart}\n            showWishlist={showWishlist}\n            onQuickView={onQuickView}\n            onAddToCart={onAddToCart}\n            onToggleWishlist={onToggleWishlist}\n            isInWishlist={wishlistItems.includes(product.id)}\n          />\n        </motion.div>\n      ))}\n    </div>\n  )\n}\n\nfunction ProductCardSkeleton() {\n  return (\n    <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n      <div className=\"aspect-square bg-gray-200 animate-pulse\" />\n      <div className=\"p-4 space-y-3\">\n        <div className=\"h-4 bg-gray-200 rounded animate-pulse\" />\n        <div className=\"h-3 bg-gray-200 rounded w-2/3 animate-pulse\" />\n        <div className=\"flex items-center space-x-1\">\n          {[...Array(5)].map((_, i) => (\n            <div key={i} className=\"h-4 w-4 bg-gray-200 rounded animate-pulse\" />\n          ))}\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"h-5 bg-gray-200 rounded w-1/3 animate-pulse\" />\n          <div className=\"h-3 bg-gray-200 rounded w-1/4 animate-pulse\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Product Grid with filters\ninterface ProductGridWithFiltersProps extends ProductGridProps {\n  title?: string\n  subtitle?: string\n  showFilters?: boolean\n  filters?: {\n    category?: string\n    priceRange?: [number, number]\n    inStock?: boolean\n    featured?: boolean\n    sortBy?: 'name' | 'price' | 'created' | 'popularity'\n    sortOrder?: 'asc' | 'desc'\n  }\n  onFiltersChange?: (filters: any) => void\n}\n\nexport function ProductGridWithFilters({\n  title,\n  subtitle,\n  showFilters = false,\n  filters,\n  onFiltersChange,\n  ...gridProps\n}: ProductGridWithFiltersProps) {\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      {(title || subtitle) && (\n        <div className=\"text-center\">\n          {title && (\n            <h2 className=\"text-3xl font-bold text-primary mb-2\">{title}</h2>\n          )}\n          {subtitle && (\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              {subtitle}\n            </p>\n          )}\n        </div>\n      )}\n\n      {/* Filters */}\n      {showFilters && (\n        <div className=\"flex flex-col sm:flex-row gap-4 items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <span className=\"text-sm text-muted-foreground\">\n              {gridProps.products.length} products\n            </span>\n          </div>\n          \n          <div className=\"flex items-center gap-4\">\n            <select\n              className=\"border border-gray-300 rounded-md px-3 py-2 text-sm\"\n              value={`${filters?.sortBy}-${filters?.sortOrder}`}\n              onChange={(e) => {\n                const [sortBy, sortOrder] = e.target.value.split('-')\n                onFiltersChange?.({ ...filters, sortBy, sortOrder })\n              }}\n            >\n              <option value=\"popularity-desc\">Most Popular</option>\n              <option value=\"created-desc\">Newest First</option>\n              <option value=\"price-asc\">Price: Low to High</option>\n              <option value=\"price-desc\">Price: High to Low</option>\n              <option value=\"name-asc\">Name: A to Z</option>\n              <option value=\"name-desc\">Name: Z to A</option>\n            </select>\n          </div>\n        </div>\n      )}\n\n      {/* Grid */}\n      <ProductGrid {...gridProps} />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAsBO,SAAS,YAAY,EAC1B,QAAQ,EACR,SAAS,EACT,UAAU,CAAC,EACX,gBAAgB,IAAI,EACpB,gBAAgB,IAAI,EACpB,eAAe,IAAI,EACnB,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EAAE,EAClB,UAAU,KAAK,EACE;IACjB,MAAM,WAAW;QACf,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc,QAAQ,CAAC,QAAQ,EAAE;sBACjD;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC,yBAAyB;;;;;;;;;;IAIlC;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;kCAIR,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAMrC;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc,QAAQ,CAAC,QAAQ,EAAE;kBACjD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;0BAEhD,cAAA,6LAAC,gJAAA,CAAA,cAAW;oBACV,SAAS;oBACT,eAAe;oBACf,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,aAAa;oBACb,kBAAkB;oBAClB,cAAc,cAAc,QAAQ,CAAC,QAAQ,EAAE;;;;;;eAb5C,QAAQ,EAAE;;;;;;;;;;AAmBzB;KAjFgB;AAmFhB,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;kCAGd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB;MAnBS;AAqCF,SAAS,uBAAuB,EACrC,KAAK,EACL,QAAQ,EACR,cAAc,KAAK,EACnB,OAAO,EACP,eAAe,EACf,GAAG,WACyB;IAC5B,qBACE,6LAAC;QAAI,WAAU;;YAEZ,CAAC,SAAS,QAAQ,mBACjB,6LAAC;gBAAI,WAAU;;oBACZ,uBACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;oBAEvD,0BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;YAOR,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;gCACb,UAAU,QAAQ,CAAC,MAAM;gCAAC;;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,SAAS,WAAW;4BACjD,UAAU,CAAC;gCACT,MAAM,CAAC,QAAQ,UAAU,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gCACjD,kBAAkB;oCAAE,GAAG,OAAO;oCAAE;oCAAQ;gCAAU;4BACpD;;8CAEA,6LAAC;oCAAO,OAAM;8CAAkB;;;;;;8CAChC,6LAAC;oCAAO,OAAM;8CAAe;;;;;;8CAC7B,6LAAC;oCAAO,OAAM;8CAAY;;;;;;8CAC1B,6LAAC;oCAAO,OAAM;8CAAa;;;;;;8CAC3B,6LAAC;oCAAO,OAAM;8CAAW;;;;;;8CACzB,6LAAC;oCAAO,OAAM;8CAAY;;;;;;;;;;;;;;;;;;;;;;;0BAOlC,6LAAC;gBAAa,GAAG,SAAS;;;;;;;;;;;;AAGhC;MAzDgB", "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/ui/Input.tsx"], "sourcesContent": ["import React, { useId } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helperText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  containerClassName?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({\n    className,\n    type = 'text',\n    label,\n    error,\n    helperText,\n    leftIcon,\n    rightIcon,\n    containerClassName,\n    id,\n    ...props\n  }, ref) => {\n    const generatedId = useId()\n    const inputId = id || generatedId\n    \n    return (\n      <div className={cn('w-full', containerClassName)}>\n        {label && (\n          <label \n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-foreground mb-2\"\n          >\n            {label}\n            {props.required && <span className=\"text-error ml-1\">*</span>}\n          </label>\n        )}\n        \n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-500 text-sm\">{leftIcon}</span>\n            </div>\n          )}\n          \n          <input\n            type={type}\n            id={inputId}\n            className={cn(\n              'flex h-10 w-full rounded-md border border-border bg-input px-3 py-2 text-sm text-foreground placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200',\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              error && 'border-error focus:ring-error',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          \n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-500 text-sm\">{rightIcon}</span>\n            </div>\n          )}\n        </div>\n        \n        {error && (\n          <p className=\"mt-1 text-sm text-error\">{error}</p>\n        )}\n        \n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-600\">{helperText}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAWA,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,CAAC,EACC,SAAS,EACT,OAAO,MAAM,EACb,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,EAAE,EACF,GAAG,OACJ,EAAE;;IACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD;IACxB,MAAM,UAAU,MAAM;IAEtB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,6LAAC;wBAAK,WAAU;kCAAkB;;;;;;;;;;;;0BAIzD,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAyB;;;;;;;;;;;kCAI7C,6LAAC;wBACC,MAAM;wBACN,IAAI;wBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gRACA,YAAY,SACZ,aAAa,SACb,SAAS,iCACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAGV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAyB;;;;;;;;;;;;;;;;;YAK9C,uBACC,6LAAC;gBAAE,WAAU;0BAA2B;;;;;;YAGzC,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;QApDsB,6JAAA,CAAA,QAAK;;;;QAAL,6JAAA,CAAA,QAAK;;;;AAsD7B,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/app/products/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { ProductGridWithFilters } from '@/components/products/ProductGrid'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { useCart } from '@/contexts/CartContext'\nimport { Search, Filter, X } from 'lucide-react'\nimport { Product, Category } from '@/types'\n\n// Mock data for demonstration\nconst mockCategories: Category[] = [\n  { id: '1', name: \"Men's Clothing\", slug: 'mens-clothing', description: '', order: 1, isActive: true },\n  { id: '2', name: \"Women's Clothing\", slug: 'womens-clothing', description: '', order: 2, isActive: true },\n  { id: '3', name: 'Accessories', slug: 'accessories', description: '', order: 3, isActive: true },\n  { id: '4', name: 'Sale', slug: 'sale', description: '', order: 4, isActive: true },\n]\n\nconst mockProducts: Product[] = [\n  {\n    id: '1',\n    name: 'Premium Cotton T-Shirt',\n    description: 'High-quality cotton t-shirt with perfect fit and comfort.',\n    price: 49.99,\n    salePrice: undefined,\n    sku: 'MEN-TSHIRT-001',\n    slug: 'premium-cotton-tshirt',\n    images: [\n      { id: '1', url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500', alt: 'Premium Cotton T-Shirt', isPrimary: true, order: 1 }\n    ],\n    category: mockCategories[0],\n    variants: [\n      { id: '1', name: 'Small', type: 'size', value: 'S', stockQuantity: 15 },\n      { id: '2', name: 'Medium', type: 'size', value: 'M', stockQuantity: 20 },\n      { id: '3', name: 'Large', type: 'size', value: 'L', stockQuantity: 15 },\n    ],\n    tags: ['cotton', 'casual', 'comfortable'],\n    inStock: true,\n    stockQuantity: 50,\n    featured: true,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n  },\n  {\n    id: '2',\n    name: 'Classic Denim Jeans',\n    description: 'Timeless denim jeans crafted from premium denim fabric.',\n    price: 89.99,\n    salePrice: 69.99,\n    sku: 'MEN-JEANS-001',\n    slug: 'classic-denim-jeans',\n    images: [\n      { id: '2', url: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500', alt: 'Classic Denim Jeans', isPrimary: true, order: 1 }\n    ],\n    category: mockCategories[0],\n    variants: [\n      { id: '4', name: '30', type: 'size', value: '30', stockQuantity: 8 },\n      { id: '5', name: '32', type: 'size', value: '32', stockQuantity: 12 },\n      { id: '6', name: '34', type: 'size', value: '34', stockQuantity: 10 },\n    ],\n    tags: ['denim', 'classic', 'versatile'],\n    inStock: true,\n    stockQuantity: 30,\n    featured: true,\n    createdAt: '2024-01-02',\n    updatedAt: '2024-01-02',\n  },\n  {\n    id: '3',\n    name: 'Elegant Silk Blouse',\n    description: 'Beautiful silk blouse with elegant draping and sophisticated design.',\n    price: 119.99,\n    salePrice: 99.99,\n    sku: 'WOM-BLOUSE-001',\n    slug: 'elegant-silk-blouse',\n    images: [\n      { id: '3', url: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500', alt: 'Elegant Silk Blouse', isPrimary: true, order: 1 }\n    ],\n    category: mockCategories[1],\n    variants: [\n      { id: '7', name: 'XS', type: 'size', value: 'XS', stockQuantity: 5 },\n      { id: '8', name: 'S', type: 'size', value: 'S', stockQuantity: 8 },\n      { id: '9', name: 'M', type: 'size', value: 'M', stockQuantity: 7 },\n    ],\n    tags: ['silk', 'elegant', 'professional'],\n    inStock: true,\n    stockQuantity: 20,\n    featured: true,\n    createdAt: '2024-01-03',\n    updatedAt: '2024-01-03',\n  },\n  {\n    id: '4',\n    name: 'Leather Crossbody Bag',\n    description: 'Stylish leather crossbody bag with multiple compartments.',\n    price: 149.99,\n    salePrice: 129.99,\n    sku: 'ACC-BAG-001',\n    slug: 'leather-crossbody-bag',\n    images: [\n      { id: '4', url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=500', alt: 'Leather Crossbody Bag', isPrimary: true, order: 1 }\n    ],\n    category: mockCategories[2],\n    variants: [\n      { id: '10', name: 'Black', type: 'color', value: 'Black', stockQuantity: 10 },\n      { id: '11', name: 'Brown', type: 'color', value: 'Brown', stockQuantity: 8 },\n    ],\n    tags: ['leather', 'bag', 'crossbody'],\n    inStock: true,\n    stockQuantity: 18,\n    featured: true,\n    createdAt: '2024-01-04',\n    updatedAt: '2024-01-04',\n  },\n]\n\nexport default function ProductsPage() {\n  const { addItem } = useCart()\n  const [products, setProducts] = useState<Product[]>(mockProducts)\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>(mockProducts)\n  const [loading, setLoading] = useState(false)\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState<string>('')\n  const [priceRange, setPriceRange] = useState<[number, number]>([0, 500])\n  const [showOnlyInStock, setShowOnlyInStock] = useState(false)\n  const [showOnlyFeatured, setShowOnlyFeatured] = useState(false)\n  const [sortBy, setSortBy] = useState<'name' | 'price' | 'created' | 'popularity'>('popularity')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n  const [showFilters, setShowFilters] = useState(false)\n\n  // Filter and sort products\n  useEffect(() => {\n    let filtered = [...products]\n\n    // Search filter\n    if (searchQuery) {\n      filtered = filtered.filter(product =>\n        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        product.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))\n      )\n    }\n\n    // Category filter\n    if (selectedCategory) {\n      filtered = filtered.filter(product => product.category.id === selectedCategory)\n    }\n\n    // Price range filter\n    filtered = filtered.filter(product => {\n      const price = product.salePrice || product.price\n      return price >= priceRange[0] && price <= priceRange[1]\n    })\n\n    // Stock filter\n    if (showOnlyInStock) {\n      filtered = filtered.filter(product => product.inStock)\n    }\n\n    // Featured filter\n    if (showOnlyFeatured) {\n      filtered = filtered.filter(product => product.featured)\n    }\n\n    // Sort products\n    filtered.sort((a, b) => {\n      let aValue: any, bValue: any\n\n      switch (sortBy) {\n        case 'name':\n          aValue = a.name\n          bValue = b.name\n          break\n        case 'price':\n          aValue = a.salePrice || a.price\n          bValue = b.salePrice || b.price\n          break\n        case 'created':\n          aValue = new Date(a.createdAt)\n          bValue = new Date(b.createdAt)\n          break\n        default:\n          aValue = a.featured ? 1 : 0\n          bValue = b.featured ? 1 : 0\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1\n      } else {\n        return aValue < bValue ? 1 : -1\n      }\n    })\n\n    setFilteredProducts(filtered)\n  }, [products, searchQuery, selectedCategory, priceRange, showOnlyInStock, showOnlyFeatured, sortBy, sortOrder])\n\n  const clearFilters = () => {\n    setSearchQuery('')\n    setSelectedCategory('')\n    setPriceRange([0, 500])\n    setShowOnlyInStock(false)\n    setShowOnlyFeatured(false)\n    setSortBy('popularity')\n    setSortOrder('desc')\n  }\n\n  const hasActiveFilters = searchQuery || selectedCategory || showOnlyInStock || showOnlyFeatured || \n    priceRange[0] > 0 || priceRange[1] < 500\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-50 to-white py-12\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl font-bold text-primary mb-4\">Our Products</h1>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Discover our complete collection of premium clothing and accessories\n            </p>\n          </motion.div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar Filters */}\n          <div className=\"lg:w-64 flex-shrink-0\">\n            <div className=\"lg:sticky lg:top-4\">\n              {/* Mobile Filter Toggle */}\n              <div className=\"lg:hidden mb-4\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowFilters(!showFilters)}\n                  className=\"w-full\"\n                >\n                  <Filter className=\"h-4 w-4 mr-2\" />\n                  Filters\n                  {hasActiveFilters && (\n                    <span className=\"ml-2 bg-accent text-primary text-xs px-2 py-1 rounded-full\">\n                      Active\n                    </span>\n                  )}\n                </Button>\n              </div>\n\n              {/* Filters */}\n              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"text-lg flex items-center justify-between\">\n                      Filters\n                      {hasActiveFilters && (\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={clearFilters}\n                          className=\"text-xs\"\n                        >\n                          Clear All\n                        </Button>\n                      )}\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    {/* Search */}\n                    <div>\n                      <label className=\"text-sm font-medium mb-2 block\">Search</label>\n                      <Input\n                        placeholder=\"Search products...\"\n                        value={searchQuery}\n                        onChange={(e) => setSearchQuery(e.target.value)}\n                        leftIcon={<Search className=\"h-4 w-4\" />}\n                      />\n                    </div>\n\n                    {/* Categories */}\n                    <div>\n                      <label className=\"text-sm font-medium mb-2 block\">Category</label>\n                      <select\n                        className=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                        value={selectedCategory}\n                        onChange={(e) => setSelectedCategory(e.target.value)}\n                      >\n                        <option value=\"\">All Categories</option>\n                        {mockCategories.map(category => (\n                          <option key={category.id} value={category.id}>\n                            {category.name}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    {/* Price Range */}\n                    <div>\n                      <label className=\"text-sm font-medium mb-2 block\">\n                        Price Range: ${priceRange[0]} - ${priceRange[1]}\n                      </label>\n                      <div className=\"space-y-2\">\n                        <input\n                          type=\"range\"\n                          min=\"0\"\n                          max=\"500\"\n                          value={priceRange[0]}\n                          onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}\n                          className=\"w-full\"\n                        />\n                        <input\n                          type=\"range\"\n                          min=\"0\"\n                          max=\"500\"\n                          value={priceRange[1]}\n                          onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}\n                          className=\"w-full\"\n                        />\n                      </div>\n                    </div>\n\n                    {/* Checkboxes */}\n                    <div className=\"space-y-2\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={showOnlyInStock}\n                          onChange={(e) => setShowOnlyInStock(e.target.checked)}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm\">In Stock Only</span>\n                      </label>\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={showOnlyFeatured}\n                          onChange={(e) => setShowOnlyFeatured(e.target.checked)}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm\">Featured Only</span>\n                      </label>\n                    </div>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            <ProductGridWithFilters\n              products={filteredProducts}\n              loading={loading}\n              showFilters={true}\n              filters={{\n                sortBy,\n                sortOrder,\n              }}\n              onFiltersChange={(filters) => {\n                setSortBy(filters.sortBy)\n                setSortOrder(filters.sortOrder)\n              }}\n              onQuickView={(product) => {\n                console.log('Quick view:', product)\n              }}\n              onAddToCart={(product) => {\n                addItem(product)\n              }}\n              onToggleWishlist={(product) => {\n                console.log('Toggle wishlist:', product)\n              }}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AATA;;;;;;;;;AAYA,8BAA8B;AAC9B,MAAM,iBAA6B;IACjC;QAAE,IAAI;QAAK,MAAM;QAAkB,MAAM;QAAiB,aAAa;QAAI,OAAO;QAAG,UAAU;IAAK;IACpG;QAAE,IAAI;QAAK,MAAM;QAAoB,MAAM;QAAmB,aAAa;QAAI,OAAO;QAAG,UAAU;IAAK;IACxG;QAAE,IAAI;QAAK,MAAM;QAAe,MAAM;QAAe,aAAa;QAAI,OAAO;QAAG,UAAU;IAAK;IAC/F;QAAE,IAAI;QAAK,MAAM;QAAQ,MAAM;QAAQ,aAAa;QAAI,OAAO;QAAG,UAAU;IAAK;CAClF;AAED,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WAAW;QACX,KAAK;QACL,MAAM;QACN,QAAQ;YACN;gBAAE,IAAI;gBAAK,KAAK;gBAAsE,KAAK;gBAA0B,WAAW;gBAAM,OAAO;YAAE;SAChJ;QACD,UAAU,cAAc,CAAC,EAAE;QAC3B,UAAU;YACR;gBAAE,IAAI;gBAAK,MAAM;gBAAS,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAG;YACtE;gBAAE,IAAI;gBAAK,MAAM;gBAAU,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAG;YACvE;gBAAE,IAAI;gBAAK,MAAM;gBAAS,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAG;SACvE;QACD,MAAM;YAAC;YAAU;YAAU;SAAc;QACzC,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WAAW;QACX,KAAK;QACL,MAAM;QACN,QAAQ;YACN;gBAAE,IAAI;gBAAK,KAAK;gBAAmE,KAAK;gBAAuB,WAAW;gBAAM,OAAO;YAAE;SAC1I;QACD,UAAU,cAAc,CAAC,EAAE;QAC3B,UAAU;YACR;gBAAE,IAAI;gBAAK,MAAM;gBAAM,MAAM;gBAAQ,OAAO;gBAAM,eAAe;YAAE;YACnE;gBAAE,IAAI;gBAAK,MAAM;gBAAM,MAAM;gBAAQ,OAAO;gBAAM,eAAe;YAAG;YACpE;gBAAE,IAAI;gBAAK,MAAM;gBAAM,MAAM;gBAAQ,OAAO;gBAAM,eAAe;YAAG;SACrE;QACD,MAAM;YAAC;YAAS;YAAW;SAAY;QACvC,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WAAW;QACX,KAAK;QACL,MAAM;QACN,QAAQ;YACN;gBAAE,IAAI;gBAAK,KAAK;gBAAsE,KAAK;gBAAuB,WAAW;gBAAM,OAAO;YAAE;SAC7I;QACD,UAAU,cAAc,CAAC,EAAE;QAC3B,UAAU;YACR;gBAAE,IAAI;gBAAK,MAAM;gBAAM,MAAM;gBAAQ,OAAO;gBAAM,eAAe;YAAE;YACnE;gBAAE,IAAI;gBAAK,MAAM;gBAAK,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAE;YACjE;gBAAE,IAAI;gBAAK,MAAM;gBAAK,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAE;SAClE;QACD,MAAM;YAAC;YAAQ;YAAW;SAAe;QACzC,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WAAW;QACX,KAAK;QACL,MAAM;QACN,QAAQ;YACN;gBAAE,IAAI;gBAAK,KAAK;gBAAsE,KAAK;gBAAyB,WAAW;gBAAM,OAAO;YAAE;SAC/I;QACD,UAAU,cAAc,CAAC,EAAE;QAC3B,UAAU;YACR;gBAAE,IAAI;gBAAM,MAAM;gBAAS,MAAM;gBAAS,OAAO;gBAAS,eAAe;YAAG;YAC5E;gBAAE,IAAI;gBAAM,MAAM;gBAAS,MAAM;gBAAS,OAAO;gBAAS,eAAe;YAAE;SAC5E;QACD,MAAM;YAAC;YAAW;YAAO;SAAY;QACrC,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW;QACX,WAAW;IACb;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAAC;QAAG;KAAI;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW;mBAAI;aAAS;YAE5B,gBAAgB;YAChB,IAAI,aAAa;gBACf,WAAW,SAAS,MAAM;8CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAClE,QAAQ,IAAI,EAAE;sDAAK,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;;YAEhF;YAEA,kBAAkB;YAClB,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM;8CAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,EAAE,KAAK;;YAChE;YAEA,qBAAqB;YACrB,WAAW,SAAS,MAAM;0CAAC,CAAA;oBACzB,MAAM,QAAQ,QAAQ,SAAS,IAAI,QAAQ,KAAK;oBAChD,OAAO,SAAS,UAAU,CAAC,EAAE,IAAI,SAAS,UAAU,CAAC,EAAE;gBACzD;;YAEA,eAAe;YACf,IAAI,iBAAiB;gBACnB,WAAW,SAAS,MAAM;8CAAC,CAAA,UAAW,QAAQ,OAAO;;YACvD;YAEA,kBAAkB;YAClB,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM;8CAAC,CAAA,UAAW,QAAQ,QAAQ;;YACxD;YAEA,gBAAgB;YAChB,SAAS,IAAI;0CAAC,CAAC,GAAG;oBAChB,IAAI,QAAa;oBAEjB,OAAQ;wBACN,KAAK;4BACH,SAAS,EAAE,IAAI;4BACf,SAAS,EAAE,IAAI;4BACf;wBACF,KAAK;4BACH,SAAS,EAAE,SAAS,IAAI,EAAE,KAAK;4BAC/B,SAAS,EAAE,SAAS,IAAI,EAAE,KAAK;4BAC/B;wBACF,KAAK;4BACH,SAAS,IAAI,KAAK,EAAE,SAAS;4BAC7B,SAAS,IAAI,KAAK,EAAE,SAAS;4BAC7B;wBACF;4BACE,SAAS,EAAE,QAAQ,GAAG,IAAI;4BAC1B,SAAS,EAAE,QAAQ,GAAG,IAAI;oBAC9B;oBAEA,IAAI,cAAc,OAAO;wBACvB,OAAO,SAAS,SAAS,IAAI,CAAC;oBAChC,OAAO;wBACL,OAAO,SAAS,SAAS,IAAI,CAAC;oBAChC;gBACF;;YAEA,oBAAoB;QACtB;iCAAG;QAAC;QAAU;QAAa;QAAkB;QAAY;QAAiB;QAAkB;QAAQ;KAAU;IAE9G,MAAM,eAAe;QACnB,eAAe;QACf,oBAAoB;QACpB,cAAc;YAAC;YAAG;SAAI;QACtB,mBAAmB;QACnB,oBAAoB;QACpB,UAAU;QACV,aAAa;IACf;IAEA,MAAM,mBAAmB,eAAe,oBAAoB,mBAAmB,oBAC7E,UAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,GAAG;IAEvC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;;;;;;;;;;;0BAOrE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,eAAe,CAAC;4CAC/B,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;gDAElC,kCACC,6LAAC;oDAAK,WAAU;8DAA6D;;;;;;;;;;;;;;;;;kDAQnF,6LAAC;wCAAI,WAAW,CAAC,UAAU,EAAE,cAAc,UAAU,mBAAmB;kDACtE,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;4DAA4C;4DAE9D,kCACC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;gEACT,WAAU;0EACX;;;;;;;;;;;;;;;;;8DAMP,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEAErB,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAClD,6LAAC,oIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oEAC9C,wBAAU,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAKhC,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAClD,6LAAC;oEACC,WAAU;oEACV,OAAO;oEACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;;sFAEnD,6LAAC;4EAAO,OAAM;sFAAG;;;;;;wEAChB,eAAe,GAAG,CAAC,CAAA,yBAClB,6LAAC;gFAAyB,OAAO,SAAS,EAAE;0FACzC,SAAS,IAAI;+EADH,SAAS,EAAE;;;;;;;;;;;;;;;;;sEAQ9B,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;;wEAAiC;wEACjC,UAAU,CAAC,EAAE;wEAAC;wEAAK,UAAU,CAAC,EAAE;;;;;;;8EAEjD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,MAAK;4EACL,KAAI;4EACJ,KAAI;4EACJ,OAAO,UAAU,CAAC,EAAE;4EACpB,UAAU,CAAC,IAAM,cAAc;oFAAC,SAAS,EAAE,MAAM,CAAC,KAAK;oFAAG,UAAU,CAAC,EAAE;iFAAC;4EACxE,WAAU;;;;;;sFAEZ,6LAAC;4EACC,MAAK;4EACL,KAAI;4EACJ,KAAI;4EACJ,OAAO,UAAU,CAAC,EAAE;4EACpB,UAAU,CAAC,IAAM,cAAc;oFAAC,UAAU,CAAC,EAAE;oFAAE,SAAS,EAAE,MAAM,CAAC,KAAK;iFAAE;4EACxE,WAAU;;;;;;;;;;;;;;;;;;sEAMhB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;;sFACf,6LAAC;4EACC,MAAK;4EACL,SAAS;4EACT,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,OAAO;4EACpD,WAAU;;;;;;sFAEZ,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAE5B,6LAAC;oEAAM,WAAU;;sFACf,6LAAC;4EACC,MAAK;4EACL,SAAS;4EACT,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,OAAO;4EACrD,WAAU;;;;;;sFAEZ,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUxC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gJAAA,CAAA,yBAAsB;gCACrB,UAAU;gCACV,SAAS;gCACT,aAAa;gCACb,SAAS;oCACP;oCACA;gCACF;gCACA,iBAAiB,CAAC;oCAChB,UAAU,QAAQ,MAAM;oCACxB,aAAa,QAAQ,SAAS;gCAChC;gCACA,aAAa,CAAC;oCACZ,QAAQ,GAAG,CAAC,eAAe;gCAC7B;gCACA,aAAa,CAAC;oCACZ,QAAQ;gCACV;gCACA,kBAAkB,CAAC;oCACjB,QAAQ,GAAG,CAAC,oBAAoB;gCAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;GAtQwB;;QACF,kIAAA,CAAA,UAAO;;;KADL", "debugId": null}}]}