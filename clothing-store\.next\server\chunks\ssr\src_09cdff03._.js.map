{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/i18n/config.ts"], "sourcesContent": ["import { notFound } from 'next/navigation'\nimport { getRequestConfig } from 'next-intl/server'\n\n// Can be imported from a shared config\nexport const locales = ['en', 'tr', 'de'] as const\nexport type Locale = typeof locales[number]\n\nexport const defaultLocale: Locale = 'en'\n\nexport const localeNames: Record<Locale, string> = {\n  en: 'English',\n  tr: 'Türkçe',\n  de: 'Deutsch',\n}\n\nexport const localeFlags: Record<Locale, string> = {\n  en: '🇺🇸',\n  tr: '🇹🇷',\n  de: '🇩🇪',\n}\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) notFound()\n\n  return {\n    messages: (await import(`./messages/${locale}.json`)).default\n  }\n})\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;;;AAGO,MAAM,UAAU;IAAC;IAAM;IAAM;CAAK;AAGlC,MAAM,gBAAwB;AAE9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;uCAEe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAmB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAEhD,OAAO;QACL,UAAU,CAAC;;;;;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/contexts/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/CartContext.tsx <module evaluation>\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/CartContext.tsx <module evaluation>\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/contexts/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/CartContext.tsx\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/CartContext.tsx\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/cart/CartSidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartSidebar() from the server but CartSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/cart/CartSidebar.tsx <module evaluation>\",\n    \"CartSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qEACA", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/cart/CartSidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartSidebar() from the server but CartSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/cart/CartSidebar.tsx\",\n    \"CartSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iDACA", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Inter, Playfair_Display } from \"next/font/google\";\nimport { NextIntlClientProvider } from 'next-intl';\nimport { getMessages } from 'next-intl/server';\nimport { CartProvider } from \"@/contexts/CartContext\";\nimport { CartSidebar } from \"@/components/cart/CartSidebar\";\nimport { locales } from '@/i18n/config';\nimport { notFound } from 'next/navigation';\nimport \"../globals.css\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nconst playfair = Playfair_Display({\n  subsets: [\"latin\"],\n  variable: \"--font-playfair\",\n});\n\nexport function generateStaticParams() {\n  return locales.map((locale) => ({ locale }));\n}\n\nexport async function generateMetadata({\n  params\n}: {\n  params: Promise<{ locale: string }>\n}): Promise<Metadata> {\n  const { locale } = await params;\n  const messages = await getMessages({ locale });\n  const title = (messages as any).meta?.title || \"EliteStore - Premium Fashion Collection\";\n  const description = (messages as any).meta?.description || \"Discover our exclusive collection of premium clothing designed for the modern lifestyle.\";\n\n  return {\n    title,\n    description,\n    keywords: [\"fashion\", \"clothing\", \"premium\", \"style\", \"modern\", \"quality\"],\n    authors: [{ name: \"EliteStore\" }],\n    creator: \"EliteStore\",\n    publisher: \"EliteStore\",\n    formatDetection: {\n      email: false,\n      address: false,\n      telephone: false,\n    },\n    metadataBase: new URL(\"https://elitestore.com\"),\n    alternates: {\n      canonical: `/${locale}`,\n      languages: {\n        \"en-US\": \"/en\",\n        \"tr-TR\": \"/tr\",\n        \"de-DE\": \"/de\",\n      },\n    },\n    openGraph: {\n      title,\n      description,\n      url: `https://elitestore.com/${locale}`,\n      siteName: \"EliteStore\",\n      images: [\n        {\n          url: \"/og-image.jpg\",\n          width: 1200,\n          height: 630,\n          alt: title,\n        },\n      ],\n      locale: locale,\n      type: \"website\",\n    },\n    twitter: {\n      card: \"summary_large_image\",\n      title,\n      description,\n      images: [\"/og-image.jpg\"],\n      creator: \"@elitestore\",\n    },\n    robots: {\n      index: true,\n      follow: true,\n      googleBot: {\n        index: true,\n        follow: true,\n        \"max-video-preview\": -1,\n        \"max-image-preview\": \"large\",\n        \"max-snippet\": -1,\n      },\n    },\n  };\n}\n\nexport default async function LocaleLayout({\n  children,\n  params\n}: {\n  children: React.ReactNode;\n  params: Promise<{ locale: string }>;\n}) {\n  const { locale } = await params;\n\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as any)) {\n    notFound();\n  }\n\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages();\n\n  return (\n    <html lang={locale} className={`${inter.variable} ${playfair.variable}`}>\n      <head>\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n        <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/apple-touch-icon.png\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/favicon-32x32.png\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/favicon-16x16.png\" />\n        <link rel=\"manifest\" href=\"/site.webmanifest\" />\n        <meta name=\"theme-color\" content=\"#FFD700\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n      </head>\n      <body className=\"font-sans antialiased\">\n        <NextIntlClientProvider messages={messages}>\n          <CartProvider>\n            {children}\n            <CartSidebar />\n          </CartProvider>\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;AAaO,SAAS;IACd,OAAO,qHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,SAAW,CAAC;YAAE;QAAO,CAAC;AAC5C;AAEO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAO;IAC5C,MAAM,QAAQ,AAAC,SAAiB,IAAI,EAAE,SAAS;IAC/C,MAAM,cAAc,AAAC,SAAiB,IAAI,EAAE,eAAe;IAE3D,OAAO;QACL;QACA;QACA,UAAU;YAAC;YAAW;YAAY;YAAW;YAAS;YAAU;SAAU;QAC1E,SAAS;YAAC;gBAAE,MAAM;YAAa;SAAE;QACjC,SAAS;QACT,WAAW;QACX,iBAAiB;YACf,OAAO;YACP,SAAS;YACT,WAAW;QACb;QACA,cAAc,IAAI,IAAI;QACtB,YAAY;YACV,WAAW,CAAC,CAAC,EAAE,QAAQ;YACvB,WAAW;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;YACX;QACF;QACA,WAAW;YACT;YACA;YACA,KAAK,CAAC,uBAAuB,EAAE,QAAQ;YACvC,UAAU;YACV,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;YACD,QAAQ;YACR,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN;YACA;YACA,QAAQ;gBAAC;aAAgB;YACzB,SAAS;QACX;QACA,QAAQ;YACN,OAAO;YACP,QAAQ;YACR,WAAW;gBACT,OAAO;gBACP,QAAQ;gBACR,qBAAqB,CAAC;gBACtB,qBAAqB;gBACrB,eAAe,CAAC;YAClB;QACF;IACF;AACF;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,yDAAyD;IACzD,IAAI,CAAC,qHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAgB;QACpC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC;QAAK,MAAM;QAAQ,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,oJAAA,CAAA,UAAQ,CAAC,QAAQ,EAAE;;0BACrE,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;wBAAK,KAAI;wBAAmB,OAAM;wBAAU,MAAK;;;;;;kCAClD,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAY,OAAM;wBAAQ,MAAK;;;;;;kCACrD,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAY,OAAM;wBAAQ,MAAK;;;;;;kCACrD,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;kCAC1B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAEhC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;oBAAC,UAAU;8BAChC,cAAA,8OAAC,+HAAA,CAAA,eAAY;;4BACV;0CACD,8OAAC,yIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB", "debugId": null}}]}