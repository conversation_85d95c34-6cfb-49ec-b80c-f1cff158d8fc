name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run type checking
      run: npm run type-check
    
    - name: Run unit tests
      run: npm run test:ci
    
    - name: Upload test coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
    
    - name: Build application
      run: npm run build
    
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30

  # Lighthouse CI is commented out until secrets are configured
  # lighthouse:
  #   runs-on: ubuntu-latest
  #   needs: test
  #   if: github.ref == 'refs/heads/main'
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4
  #
  #   - name: Setup Node.js
  #     uses: actions/setup-node@v4
  #     with:
  #       node-version: '20.x'
  #       cache: 'npm'
  #
  #   - name: Install dependencies
  #     run: npm ci
  #
  #   - name: Build application
  #     run: npm run build
  #
  #   - name: Start application
  #     run: npm start &
  #
  #   - name: Wait for server
  #     run: npx wait-on http://localhost:3000
  #
  #   - name: Run Lighthouse CI
  #     run: |
  #       npm install -g @lhci/cli@0.12.x
  #       lhci autorun
  #     env:
  #       LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  security:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run security audit
      run: npm audit --audit-level high
    
    - name: Run dependency check
      run: npx audit-ci --config audit-ci.json

  # Deployment jobs are commented out until secrets are configured
  # deploy-preview:
  #   runs-on: ubuntu-latest
  #   needs: [test, security]
  #   if: github.event_name == 'pull_request'
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4
  #
  #   - name: Deploy to Vercel Preview
  #     uses: amondnet/vercel-action@v25
  #     with:
  #       vercel-token: ${{ secrets.VERCEL_TOKEN }}
  #       vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
  #       vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
  #       scope: ${{ secrets.VERCEL_ORG_ID }}

  # deploy-production:
  #   runs-on: ubuntu-latest
  #   needs: [test, security, lighthouse]
  #   if: github.ref == 'refs/heads/main'
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4
  #
  #   - name: Deploy to Vercel Production
  #     uses: amondnet/vercel-action@v25
  #     with:
  #       vercel-token: ${{ secrets.VERCEL_TOKEN }}
  #       vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
  #       vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
  #       vercel-args: '--prod'
  #       scope: ${{ secrets.VERCEL_ORG_ID }}

  #   - name: Create GitHub Release
  #     if: success()
  #     uses: actions/create-release@v1
  #     env:
  #       GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  #     with:
  #       tag_name: v${{ github.run_number }}
  #       release_name: Release v${{ github.run_number }}
  #       body: |
  #         Automated release created by GitHub Actions
  #
  #         Changes in this release:
  #         ${{ github.event.head_commit.message }}
  #       draft: false
  #       prerelease: false
