"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var program_exports = {};
__export(program_exports, {
  program: () => import_program2.program,
  withRunnerAndMutedWrite: () => withRunnerAndMutedWrite
});
module.exports = __toCommonJS(program_exports);
var import_fs = __toESM(require("fs"));
var import_path = __toESM(require("path"));
var import_program = require("playwright-core/lib/cli/program");
var import_utils = require("playwright-core/lib/utils");
var import_config = require("./common/config");
var import_configLoader = require("./common/configLoader");
var import_program2 = require("playwright-core/lib/cli/program");
var import_base = require("./reporters/base");
var import_html = require("./reporters/html");
var import_merge = require("./reporters/merge");
var import_projectUtils = require("./runner/projectUtils");
var import_runner = require("./runner/runner");
var testServer = __toESM(require("./runner/testServer"));
var import_watchMode = require("./runner/watchMode");
var import_util = require("./util");
function addTestCommand(program3) {
  const command = program3.command("test [test-filter...]");
  command.description("run tests with Playwright Test");
  const options = testOptions.sort((a, b) => a[0].replace(/-/g, "").localeCompare(b[0].replace(/-/g, "")));
  options.forEach(([name, description]) => command.option(name, description));
  command.action(async (args, opts) => {
    try {
      await runTests(args, opts);
    } catch (e) {
      console.error(e);
      (0, import_utils.gracefullyProcessExitDoNotHang)(1);
    }
  });
  command.addHelpText("afterAll", `
Arguments [test-filter...]:
  Pass arguments to filter test files. Each argument is treated as a regular expression. Matching is performed against the absolute file paths.

Examples:
  $ npx playwright test my.spec.ts
  $ npx playwright test some.spec.ts:42
  $ npx playwright test --headed
  $ npx playwright test --project=webkit`);
}
function addListFilesCommand(program3) {
  const command = program3.command("list-files [file-filter...]", { hidden: true });
  command.description("List files with Playwright Test tests");
  command.option("-c, --config <file>", `Configuration file, or a test directory with optional "playwright.config.{m,c}?{js,ts}"`);
  command.option("--project <project-name...>", `Only run tests from the specified list of projects, supports '*' wildcard (default: list all projects)`);
  command.action(async (args, opts) => listTestFiles(opts));
}
function addClearCacheCommand(program3) {
  const command = program3.command("clear-cache");
  command.description("clears build and test caches");
  command.option("-c, --config <file>", `Configuration file, or a test directory with optional "playwright.config.{m,c}?{js,ts}"`);
  command.action(async (opts) => {
    const config = await (0, import_configLoader.loadConfigFromFile)(opts.config);
    const runner = new import_runner.Runner(config);
    const { status } = await runner.clearCache();
    const exitCode = status === "interrupted" ? 130 : status === "passed" ? 0 : 1;
    (0, import_utils.gracefullyProcessExitDoNotHang)(exitCode);
  });
}
function addFindRelatedTestFilesCommand(program3) {
  const command = program3.command("find-related-test-files [source-files...]", { hidden: true });
  command.description("Returns the list of related tests to the given files");
  command.option("-c, --config <file>", `Configuration file, or a test directory with optional "playwright.config.{m,c}?{js,ts}"`);
  command.action(async (files, options) => {
    const resolvedFiles = files.map((file) => import_path.default.resolve(process.cwd(), file));
    await withRunnerAndMutedWrite(options.config, (runner) => runner.findRelatedTestFiles(resolvedFiles));
  });
}
function addDevServerCommand(program3) {
  const command = program3.command("dev-server", { hidden: true });
  command.description("start dev server");
  command.option("-c, --config <file>", `Configuration file, or a test directory with optional "playwright.config.{m,c}?{js,ts}"`);
  command.action(async (options) => {
    const config = await (0, import_configLoader.loadConfigFromFile)(options.config);
    const runner = new import_runner.Runner(config);
    const { status } = await runner.runDevServer();
    const exitCode = status === "interrupted" ? 130 : status === "passed" ? 0 : 1;
    (0, import_utils.gracefullyProcessExitDoNotHang)(exitCode);
  });
}
function addTestServerCommand(program3) {
  const command = program3.command("test-server", { hidden: true });
  command.description("start test server");
  command.option("-c, --config <file>", `Configuration file, or a test directory with optional "playwright.config.{m,c}?{js,ts}"`);
  command.option("--host <host>", "Host to start the server on", "localhost");
  command.option("--port <port>", "Port to start the server on", "0");
  command.action((opts) => runTestServer(opts));
}
function addShowReportCommand(program3) {
  const command = program3.command("show-report [report]");
  command.description("show HTML report");
  command.action((report, options) => (0, import_html.showHTMLReport)(report, options.host, +options.port));
  command.option("--host <host>", "Host to serve report on", "localhost");
  command.option("--port <port>", "Port to serve report on", "9323");
  command.addHelpText("afterAll", `
Arguments [report]:
  When specified, opens given report, otherwise opens last generated report.

Examples:
  $ npx playwright show-report
  $ npx playwright show-report playwright-report`);
}
function addMergeReportsCommand(program3) {
  const command = program3.command("merge-reports [dir]");
  command.description("merge multiple blob reports (for sharded tests) into a single report");
  command.action(async (dir, options) => {
    try {
      await mergeReports(dir, options);
    } catch (e) {
      console.error(e);
      (0, import_utils.gracefullyProcessExitDoNotHang)(1);
    }
  });
  command.option("-c, --config <file>", `Configuration file. Can be used to specify additional configuration for the output report.`);
  command.option("--reporter <reporter>", `Reporter to use, comma-separated, can be ${import_config.builtInReporters.map((name) => `"${name}"`).join(", ")} (default: "${import_config.defaultReporter}")`);
  command.addHelpText("afterAll", `
Arguments [dir]:
  Directory containing blob reports.

Examples:
  $ npx playwright merge-reports playwright-report`);
}
async function runTests(args, opts) {
  await (0, import_utils.startProfiling)();
  const cliOverrides = overridesFromOptions(opts);
  const config = await (0, import_configLoader.loadConfigFromFile)(opts.config, cliOverrides, opts.deps === false);
  config.cliArgs = args;
  config.cliGrep = opts.grep;
  config.cliOnlyChanged = opts.onlyChanged === true ? "HEAD" : opts.onlyChanged;
  config.cliGrepInvert = opts.grepInvert;
  config.cliListOnly = !!opts.list;
  config.cliProjectFilter = opts.project || void 0;
  config.cliPassWithNoTests = !!opts.passWithNoTests;
  config.cliLastFailed = !!opts.lastFailed;
  (0, import_projectUtils.filterProjects)(config.projects, config.cliProjectFilter);
  if (opts.ui || opts.uiHost || opts.uiPort) {
    if (opts.onlyChanged)
      throw new Error(`--only-changed is not supported in UI mode. If you'd like that to change, see https://github.com/microsoft/playwright/issues/15075 for more details.`);
    const status2 = await testServer.runUIMode(opts.config, cliOverrides, {
      host: opts.uiHost,
      port: opts.uiPort ? +opts.uiPort : void 0,
      args,
      grep: opts.grep,
      grepInvert: opts.grepInvert,
      project: opts.project || void 0,
      reporter: Array.isArray(opts.reporter) ? opts.reporter : opts.reporter ? [opts.reporter] : void 0
    });
    await (0, import_utils.stopProfiling)("runner");
    const exitCode2 = status2 === "interrupted" ? 130 : status2 === "passed" ? 0 : 1;
    (0, import_utils.gracefullyProcessExitDoNotHang)(exitCode2);
    return;
  }
  if (process.env.PWTEST_WATCH) {
    if (opts.onlyChanged)
      throw new Error(`--only-changed is not supported in watch mode. If you'd like that to change, file an issue and let us know about your usecase for it.`);
    const status2 = await (0, import_watchMode.runWatchModeLoop)(
      (0, import_configLoader.resolveConfigLocation)(opts.config),
      {
        projects: opts.project,
        files: args,
        grep: opts.grep
      }
    );
    await (0, import_utils.stopProfiling)("runner");
    const exitCode2 = status2 === "interrupted" ? 130 : status2 === "passed" ? 0 : 1;
    (0, import_utils.gracefullyProcessExitDoNotHang)(exitCode2);
    return;
  }
  const runner = new import_runner.Runner(config);
  const status = await runner.runAllTests();
  await (0, import_utils.stopProfiling)("runner");
  const exitCode = status === "interrupted" ? 130 : status === "passed" ? 0 : 1;
  (0, import_utils.gracefullyProcessExitDoNotHang)(exitCode);
}
async function runTestServer(opts) {
  const host = opts.host || "localhost";
  const port = opts.port ? +opts.port : 0;
  const status = await testServer.runTestServer(opts.config, {}, { host, port });
  const exitCode = status === "interrupted" ? 130 : status === "passed" ? 0 : 1;
  (0, import_utils.gracefullyProcessExitDoNotHang)(exitCode);
}
async function withRunnerAndMutedWrite(configFile, callback) {
  const stdoutWrite = process.stdout.write.bind(process.stdout);
  process.stdout.write = (a, b, c) => process.stderr.write(a, b, c);
  try {
    const config = await (0, import_configLoader.loadConfigFromFile)(configFile);
    const runner = new import_runner.Runner(config);
    const result = await callback(runner);
    stdoutWrite(JSON.stringify(result, void 0, 2), () => {
      (0, import_utils.gracefullyProcessExitDoNotHang)(0);
    });
  } catch (e) {
    const error = (0, import_util.serializeError)(e);
    error.location = (0, import_base.prepareErrorStack)(e.stack).location;
    stdoutWrite(JSON.stringify({ error }, void 0, 2), () => {
      (0, import_utils.gracefullyProcessExitDoNotHang)(0);
    });
  }
}
async function listTestFiles(opts) {
  await withRunnerAndMutedWrite(opts.config, async (runner) => {
    return await runner.listTestFiles();
  });
}
async function mergeReports(reportDir, opts) {
  const configFile = opts.config;
  const config = configFile ? await (0, import_configLoader.loadConfigFromFile)(configFile) : await (0, import_configLoader.loadEmptyConfigForMergeReports)();
  const dir = import_path.default.resolve(process.cwd(), reportDir || "");
  const dirStat = await import_fs.default.promises.stat(dir).catch((e) => null);
  if (!dirStat)
    throw new Error("Directory does not exist: " + dir);
  if (!dirStat.isDirectory())
    throw new Error(`"${dir}" is not a directory`);
  let reporterDescriptions = resolveReporterOption(opts.reporter);
  if (!reporterDescriptions && configFile)
    reporterDescriptions = config.config.reporter;
  if (!reporterDescriptions)
    reporterDescriptions = [[import_config.defaultReporter]];
  const rootDirOverride = configFile ? config.config.rootDir : void 0;
  await (0, import_merge.createMergedReport)(config, dir, reporterDescriptions, rootDirOverride);
  (0, import_utils.gracefullyProcessExitDoNotHang)(0);
}
function overridesFromOptions(options) {
  let updateSnapshots;
  if (["all", "changed", "missing", "none"].includes(options.updateSnapshots))
    updateSnapshots = options.updateSnapshots;
  else
    updateSnapshots = "updateSnapshots" in options ? "changed" : void 0;
  const overrides = {
    failOnFlakyTests: options.failOnFlakyTests ? true : void 0,
    forbidOnly: options.forbidOnly ? true : void 0,
    fullyParallel: options.fullyParallel ? true : void 0,
    globalTimeout: options.globalTimeout ? parseInt(options.globalTimeout, 10) : void 0,
    maxFailures: options.x ? 1 : options.maxFailures ? parseInt(options.maxFailures, 10) : void 0,
    outputDir: options.output ? import_path.default.resolve(process.cwd(), options.output) : void 0,
    quiet: options.quiet ? options.quiet : void 0,
    repeatEach: options.repeatEach ? parseInt(options.repeatEach, 10) : void 0,
    retries: options.retries ? parseInt(options.retries, 10) : void 0,
    reporter: resolveReporterOption(options.reporter),
    shard: resolveShardOption(options.shard),
    timeout: options.timeout ? parseInt(options.timeout, 10) : void 0,
    tsconfig: options.tsconfig ? import_path.default.resolve(process.cwd(), options.tsconfig) : void 0,
    ignoreSnapshots: options.ignoreSnapshots ? !!options.ignoreSnapshots : void 0,
    updateSnapshots,
    updateSourceMethod: options.updateSourceMethod,
    workers: options.workers
  };
  if (options.browser) {
    const browserOpt = options.browser.toLowerCase();
    if (!["all", "chromium", "firefox", "webkit"].includes(browserOpt))
      throw new Error(`Unsupported browser "${options.browser}", must be one of "all", "chromium", "firefox" or "webkit"`);
    const browserNames = browserOpt === "all" ? ["chromium", "firefox", "webkit"] : [browserOpt];
    overrides.projects = browserNames.map((browserName) => {
      return {
        name: browserName,
        use: { browserName }
      };
    });
  }
  if (options.headed || options.debug)
    overrides.use = { headless: false };
  if (!options.ui && options.debug) {
    overrides.debug = true;
    process.env.PWDEBUG = "1";
  }
  if (!options.ui && options.trace) {
    if (!kTraceModes.includes(options.trace))
      throw new Error(`Unsupported trace mode "${options.trace}", must be one of ${kTraceModes.map((mode) => `"${mode}"`).join(", ")}`);
    overrides.use = overrides.use || {};
    overrides.use.trace = options.trace;
  }
  if (overrides.tsconfig && !import_fs.default.existsSync(overrides.tsconfig))
    throw new Error(`--tsconfig "${options.tsconfig}" does not exist`);
  return overrides;
}
function resolveReporterOption(reporter) {
  if (!reporter || !reporter.length)
    return void 0;
  return reporter.split(",").map((r) => [resolveReporter(r)]);
}
function resolveShardOption(shard) {
  if (!shard)
    return void 0;
  const shardPair = shard.split("/");
  if (shardPair.length !== 2) {
    throw new Error(
      `--shard "${shard}", expected format is "current/all", 1-based, for example "3/5".`
    );
  }
  const current = parseInt(shardPair[0], 10);
  const total = parseInt(shardPair[1], 10);
  if (isNaN(total) || total < 1)
    throw new Error(`--shard "${shard}" total must be a positive number`);
  if (isNaN(current) || current < 1 || current > total) {
    throw new Error(
      `--shard "${shard}" current must be a positive number, not greater than shard total`
    );
  }
  return { current, total };
}
function resolveReporter(id) {
  if (import_config.builtInReporters.includes(id))
    return id;
  const localPath = import_path.default.resolve(process.cwd(), id);
  if (import_fs.default.existsSync(localPath))
    return localPath;
  return require.resolve(id, { paths: [process.cwd()] });
}
const kTraceModes = ["on", "off", "on-first-retry", "on-all-retries", "retain-on-failure", "retain-on-first-failure"];
const testOptions = [
  /* deprecated */
  ["--browser <browser>", `Browser to use for tests, one of "all", "chromium", "firefox" or "webkit" (default: "chromium")`],
  ["-c, --config <file>", `Configuration file, or a test directory with optional "playwright.config.{m,c}?{js,ts}"`],
  ["--debug", `Run tests with Playwright Inspector. Shortcut for "PWDEBUG=1" environment variable and "--timeout=0 --max-failures=1 --headed --workers=1" options`],
  ["--fail-on-flaky-tests", `Fail if any test is flagged as flaky (default: false)`],
  ["--forbid-only", `Fail if test.only is called (default: false)`],
  ["--fully-parallel", `Run all tests in parallel (default: false)`],
  ["--global-timeout <timeout>", `Maximum time this test suite can run in milliseconds (default: unlimited)`],
  ["-g, --grep <grep>", `Only run tests matching this regular expression (default: ".*")`],
  ["-gv, --grep-invert <grep>", `Only run tests that do not match this regular expression`],
  ["--headed", `Run tests in headed browsers (default: headless)`],
  ["--ignore-snapshots", `Ignore screenshot and snapshot expectations`],
  ["--last-failed", `Only re-run the failures`],
  ["--list", `Collect all the tests and report them, but do not run`],
  ["--max-failures <N>", `Stop after the first N failures`],
  ["--no-deps", "Do not run project dependencies"],
  ["--output <dir>", `Folder for output artifacts (default: "test-results")`],
  ["--only-changed [ref]", `Only run test files that have been changed between 'HEAD' and 'ref'. Defaults to running all uncommitted changes. Only supports Git.`],
  ["--pass-with-no-tests", `Makes test run succeed even if no tests were found`],
  ["--project <project-name...>", `Only run tests from the specified list of projects, supports '*' wildcard (default: run all projects)`],
  ["--quiet", `Suppress stdio`],
  ["--repeat-each <N>", `Run each test N times (default: 1)`],
  ["--reporter <reporter>", `Reporter to use, comma-separated, can be ${import_config.builtInReporters.map((name) => `"${name}"`).join(", ")} (default: "${import_config.defaultReporter}")`],
  ["--retries <retries>", `Maximum retry count for flaky tests, zero for no retries (default: no retries)`],
  ["--shard <shard>", `Shard tests and execute only the selected shard, specify in the form "current/all", 1-based, for example "3/5"`],
  ["--timeout <timeout>", `Specify test timeout threshold in milliseconds, zero for unlimited (default: ${import_config.defaultTimeout})`],
  ["--trace <mode>", `Force tracing mode, can be ${kTraceModes.map((mode) => `"${mode}"`).join(", ")}`],
  ["--tsconfig <path>", `Path to a single tsconfig applicable to all imported files (default: look up tsconfig for each imported file separately)`],
  ["--ui", `Run tests in interactive UI mode`],
  ["--ui-host <host>", "Host to serve UI on; specifying this option opens UI in a browser tab"],
  ["--ui-port <port>", "Port to serve UI on, 0 for any free port; specifying this option opens UI in a browser tab"],
  ["-u, --update-snapshots [mode]", `Update snapshots with actual results. Possible values are "all", "changed", "missing", and "none". Running tests without the flag defaults to "missing"; running tests with the flag but without a value defaults to "changed".`],
  ["--update-source-method <method>", `Chooses the way source is updated. Possible values are 'overwrite', '3way' and 'patch'. Defaults to 'patch'`],
  ["-j, --workers <workers>", `Number of concurrent workers or percentage of logical CPU cores, use 1 to run in a single worker (default: 50%)`],
  ["-x", `Stop after the first failure`]
];
addTestCommand(import_program.program);
addShowReportCommand(import_program.program);
addListFilesCommand(import_program.program);
addMergeReportsCommand(import_program.program);
addClearCacheCommand(import_program.program);
addFindRelatedTestFilesCommand(import_program.program);
addDevServerCommand(import_program.program);
addTestServerCommand(import_program.program);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  program,
  withRunnerAndMutedWrite
});
