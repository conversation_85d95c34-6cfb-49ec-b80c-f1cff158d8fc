import { test, expect } from '@playwright/test'

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/en')
  })

  test('should load homepage successfully', async ({ page }) => {
    // Check if the page loads
    await expect(page).toHaveTitle(/EliteStore/)
    
    // Check if main elements are visible
    await expect(page.getByRole('heading', { name: /EliteStore/i })).toBeVisible()
    await expect(page.getByText(/Discover Premium/i)).toBeVisible()
  })

  test('should have working navigation', async ({ page }) => {
    // Check navigation links
    await expect(page.getByRole('link', { name: /Home/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /Men/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /Women/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /Accessories/i })).toBeVisible()
  })

  test('should have working search functionality', async ({ page }) => {
    const searchInput = page.getByPlaceholder(/Search products/i)
    await expect(searchInput).toBeVisible()
    
    // Test search input
    await searchInput.fill('shirt')
    await expect(searchInput).toHaveValue('shirt')
  })

  test('should display featured products', async ({ page }) => {
    // Wait for products to load
    await page.waitForSelector('[data-testid="product-grid"]', { timeout: 10000 })
    
    // Check if products are displayed
    const products = page.locator('[data-testid="product-card"]')
    await expect(products).toHaveCount(4) // We have 4 featured products
    
    // Check product elements
    const firstProduct = products.first()
    await expect(firstProduct.getByRole('img')).toBeVisible()
    await expect(firstProduct.getByText(/Premium Cotton T-Shirt/i)).toBeVisible()
    await expect(firstProduct.getByText(/\$49\.99/)).toBeVisible()
  })

  test('should have working cart functionality', async ({ page }) => {
    // Check cart button
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2) // Cart icon button
    await expect(cartButton).toBeVisible()
    
    // Cart should show 0 items initially
    await expect(cartButton.locator('span')).not.toBeVisible()
  })

  test('should have responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if mobile menu button is visible
    const mobileMenuButton = page.getByRole('button').filter({ has: page.locator('svg') }).last()
    await expect(mobileMenuButton).toBeVisible()
    
    // Check if desktop navigation is hidden
    await expect(page.getByRole('navigation')).toBeHidden()
  })

  test('should have working newsletter signup', async ({ page }) => {
    // Scroll to newsletter section
    await page.getByText(/Stay Updated/i).scrollIntoViewIfNeeded()
    
    // Check newsletter elements
    await expect(page.getByText(/Stay Updated/i)).toBeVisible()
    await expect(page.getByPlaceholder(/Enter your email/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /Subscribe/i })).toBeVisible()
    
    // Test email input
    const emailInput = page.getByPlaceholder(/Enter your email/i)
    await emailInput.fill('<EMAIL>')
    await expect(emailInput).toHaveValue('<EMAIL>')
  })

  test('should have working footer links', async ({ page }) => {
    // Scroll to footer
    await page.getByText(/© 2024 EliteStore/i).scrollIntoViewIfNeeded()
    
    // Check footer sections
    await expect(page.getByText(/Shop/i).nth(1)).toBeVisible() // Footer shop section
    await expect(page.getByText(/Support/i)).toBeVisible()
    await expect(page.getByText(/Company/i)).toBeVisible()
    
    // Check footer links
    await expect(page.getByRole('link', { name: /Men's Clothing/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /Women's Clothing/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /Contact Us/i })).toBeVisible()
  })

  test('should load quickly', async ({ page }) => {
    const startTime = Date.now()
    await page.goto('/en')
    await page.waitForLoadState('networkidle')
    const loadTime = Date.now() - startTime
    
    // Page should load within 3 seconds
    expect(loadTime).toBeLessThan(3000)
  })
})
