const defaultKeyMap = [
    {
        name: '<PERSON><PERSON>ef<PERSON>',
        pointerType: 'mouse',
        button: 'primary'
    },
    {
        name: 'MouseR<PERSON>',
        pointerType: 'mouse',
        button: 'secondary'
    },
    {
        name: 'MouseMiddle',
        pointerType: 'mouse',
        button: 'auxiliary'
    },
    {
        name: 'Touch<PERSON>',
        pointerType: 'touch'
    },
    {
        name: 'TouchB',
        pointerType: 'touch'
    },
    {
        name: 'TouchC',
        pointerType: 'touch'
    }
];

export { defaultKeyMap };
