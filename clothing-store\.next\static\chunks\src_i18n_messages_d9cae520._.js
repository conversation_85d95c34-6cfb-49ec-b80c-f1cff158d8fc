(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/i18n/messages/de.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_i18n_messages_de_json_32eeb7ec._.js",
  "static/chunks/src_i18n_messages_de_json_73043a1a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/i18n/messages/de.json (json)");
    });
});
}}),
"[project]/src/i18n/messages/en.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_i18n_messages_en_json_94088789._.js",
  "static/chunks/src_i18n_messages_en_json_73043a1a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/i18n/messages/en.json (json)");
    });
});
}}),
"[project]/src/i18n/messages/tr.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_i18n_messages_tr_json_b95d4efa._.js",
  "static/chunks/src_i18n_messages_tr_json_73043a1a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/i18n/messages/tr.json (json)");
    });
});
}}),
}]);