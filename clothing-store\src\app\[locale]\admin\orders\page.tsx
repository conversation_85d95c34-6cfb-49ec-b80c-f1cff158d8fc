'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Search, 
  Filter, 
  Eye, 
  Download,
  ShoppingCart,
  Clock,
  CheckCircle,
  Truck,
  AlertCircle,
  MoreHorizontal
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { formatPrice } from '@/lib/utils'

// Mock orders data
const orders = [
  {
    id: 'ORD-001',
    orderNumber: '#1001',
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    items: 3,
    total: 299.99,
    status: 'completed',
    paymentStatus: 'paid',
    shippingMethod: 'Standard Shipping',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-16T14:20:00Z',
  },
  {
    id: 'ORD-002',
    orderNumber: '#1002',
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    items: 2,
    total: 159.99,
    status: 'processing',
    paymentStatus: 'paid',
    shippingMethod: 'Express Shipping',
    createdAt: '2024-01-15T14:45:00Z',
    updatedAt: '2024-01-15T14:45:00Z',
  },
  {
    id: 'ORD-003',
    orderNumber: '#1003',
    customer: {
      name: 'Mike Johnson',
      email: '<EMAIL>',
    },
    items: 1,
    total: 89.99,
    status: 'shipped',
    paymentStatus: 'paid',
    shippingMethod: 'Standard Shipping',
    createdAt: '2024-01-14T09:15:00Z',
    updatedAt: '2024-01-15T11:30:00Z',
  },
  {
    id: 'ORD-004',
    orderNumber: '#1004',
    customer: {
      name: 'Sarah Wilson',
      email: '<EMAIL>',
    },
    items: 4,
    total: 199.99,
    status: 'pending',
    paymentStatus: 'pending',
    shippingMethod: 'Standard Shipping',
    createdAt: '2024-01-14T16:20:00Z',
    updatedAt: '2024-01-14T16:20:00Z',
  },
  {
    id: 'ORD-005',
    orderNumber: '#1005',
    customer: {
      name: 'David Brown',
      email: '<EMAIL>',
    },
    items: 2,
    total: 249.99,
    status: 'cancelled',
    paymentStatus: 'refunded',
    shippingMethod: 'Express Shipping',
    createdAt: '2024-01-13T11:10:00Z',
    updatedAt: '2024-01-14T09:45:00Z',
  },
]

const statusConfig = {
  pending: { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-100' },
  processing: { icon: ShoppingCart, color: 'text-blue-600', bg: 'bg-blue-100' },
  shipped: { icon: Truck, color: 'text-purple-600', bg: 'bg-purple-100' },
  completed: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100' },
  cancelled: { icon: AlertCircle, color: 'text-red-600', bg: 'bg-red-100' },
}

export default function OrdersPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('')

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customer.email.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !selectedStatus || order.status === selectedStatus
    const matchesPaymentStatus = !selectedPaymentStatus || order.paymentStatus === selectedPaymentStatus
    
    return matchesSearch && matchesStatus && matchesPaymentStatus
  })

  const statuses = [...new Set(orders.map(o => o.status))]
  const paymentStatuses = [...new Set(orders.map(o => o.paymentStatus))]

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Orders</h1>
          <p className="text-gray-600 mt-2">
            Manage customer orders and fulfillment
          </p>
        </div>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export Orders
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">{orders.length}</p>
              </div>
              <ShoppingCart className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        {Object.entries(statusConfig).map(([status, config]) => {
          const count = orders.filter(o => o.status === status).length
          const Icon = config.icon
          return (
            <Card key={status}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 capitalize">
                      {status.replace('_', ' ')}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">{count}</p>
                  </div>
                  <Icon className={`h-8 w-8 ${config.color}`} />
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="Search orders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
            
            <select
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              <option value="">All Status</option>
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
            
            <select
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              value={selectedPaymentStatus}
              onChange={(e) => setSelectedPaymentStatus(e.target.value)}
            >
              <option value="">All Payment Status</option>
              {paymentStatuses.map(status => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
            
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Date Range
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>Orders ({filteredOrders.length})</CardTitle>
          <CardDescription>
            Recent orders from your customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredOrders.map((order, index) => {
              const StatusIcon = statusConfig[order.status as keyof typeof statusConfig].icon
              const statusColor = statusConfig[order.status as keyof typeof statusConfig].color
              const statusBg = statusConfig[order.status as keyof typeof statusConfig].bg
              
              return (
                <motion.div
                  key={order.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {/* Order Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3">
                      <h3 className="font-medium text-gray-900">{order.orderNumber}</h3>
                      <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs rounded-full ${statusBg} ${statusColor}`}>
                        <StatusIcon className="h-3 w-3" />
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{order.customer.name}</p>
                    <p className="text-xs text-gray-500">{order.customer.email}</p>
                  </div>

                  {/* Items */}
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{order.items}</p>
                    <p className="text-xs text-gray-500">items</p>
                  </div>

                  {/* Total */}
                  <div className="text-right">
                    <p className="font-medium text-gray-900">{formatPrice(order.total)}</p>
                    <p className={`text-xs ${
                      order.paymentStatus === 'paid' ? 'text-green-600' :
                      order.paymentStatus === 'pending' ? 'text-yellow-600' :
                      'text-red-600'
                    }`}>
                      {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                    </p>
                  </div>

                  {/* Date */}
                  <div className="text-right">
                    <p className="text-sm text-gray-900">{formatDate(order.createdAt)}</p>
                    <p className="text-xs text-gray-500">{order.shippingMethod}</p>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </motion.div>
              )
            })}
          </div>

          {filteredOrders.length === 0 && (
            <div className="text-center py-12">
              <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
