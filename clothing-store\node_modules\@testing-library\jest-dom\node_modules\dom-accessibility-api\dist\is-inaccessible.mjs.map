{"version": 3, "file": "is-inaccessible.mjs", "names": ["isInaccessible", "element", "_element$ownerDocumen", "options", "arguments", "length", "undefined", "_options$getComputedS", "getComputedStyle", "ownerDocument", "defaultView", "_options$isSubtreeIna", "isSubtreeInaccessible", "isSubtreeInaccessibleImpl", "TypeError", "visibility", "currentElement", "parentElement", "_element$ownerDocumen2", "_options$getComputedS2", "hidden", "getAttribute", "display"], "sources": ["../sources/is-inaccessible.ts"], "sourcesContent": ["export interface IsInaccessibleOptions {\n\tgetComputedStyle?: typeof window.getComputedStyle;\n\t/**\n\t * Can be used to return cached results from previous isSubtreeInaccessible calls.\n\t */\n\tisSubtreeInaccessible?: (element: Element) => boolean;\n}\n\n/**\n * Partial implementation https://www.w3.org/TR/wai-aria-1.2/#tree_exclusion\n * which should only be used for elements with a non-presentational role i.e.\n * `role=\"none\"` and `role=\"presentation\"` will not be excluded.\n *\n * Implements aria-hidden semantics (i.e. parent overrides child)\n * Ignores \"Child Presentational: True\" characteristics\n *\n * @param element\n * @param options\n * @returns {boolean} true if excluded, otherwise false\n */\nexport function isInaccessible(\n\telement: Element,\n\toptions: IsInaccessibleOptions = {},\n): boolean {\n\tconst {\n\t\tgetComputedStyle = element.ownerDocument.defaultView?.getComputedStyle,\n\t\tisSubtreeInaccessible: isSubtreeInaccessibleImpl = isSubtreeInaccessible,\n\t} = options;\n\tif (typeof getComputedStyle !== \"function\") {\n\t\tthrow new TypeError(\n\t\t\t\"Owner document of the element needs to have an associated window.\",\n\t\t);\n\t}\n\t// since visibility is inherited we can exit early\n\tif (getComputedStyle(element).visibility === \"hidden\") {\n\t\treturn true;\n\t}\n\n\tlet currentElement: Element | null = element;\n\twhile (currentElement) {\n\t\tif (isSubtreeInaccessibleImpl(currentElement, { getComputedStyle })) {\n\t\t\treturn true;\n\t\t}\n\n\t\tcurrentElement = currentElement.parentElement;\n\t}\n\n\treturn false;\n}\n\nexport interface IsSubtreeInaccessibleOptions {\n\tgetComputedStyle?: typeof window.getComputedStyle;\n}\n\n/**\n *\n * @param element\n * @param options\n * @returns {boolean} - `true` if every child of the element is inaccessible\n */\nexport function isSubtreeInaccessible(\n\telement: Element,\n\toptions: IsSubtreeInaccessibleOptions = {},\n): boolean {\n\tconst {\n\t\tgetComputedStyle = element.ownerDocument.defaultView?.getComputedStyle,\n\t} = options;\n\tif (typeof getComputedStyle !== \"function\") {\n\t\tthrow new TypeError(\n\t\t\t\"Owner document of the element needs to have an associated window.\",\n\t\t);\n\t}\n\n\tif ((element as HTMLElement).hidden === true) {\n\t\treturn true;\n\t}\n\n\tif (element.getAttribute(\"aria-hidden\") === \"true\") {\n\t\treturn true;\n\t}\n\n\tif (getComputedStyle(element).display === \"none\") {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n"], "mappings": "AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,cAAcA,CAC7BC,OAAgB,EAEN;EAAA,IAAAC,qBAAA;EAAA,IADVC,OAA8B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAEnC,IAAAG,qBAAA,GAGIJ,OAAO,CAFVK,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,eAAAL,qBAAA,GAAGD,OAAO,CAACQ,aAAa,CAACC,WAAW,cAAAR,qBAAA,uBAAjCA,qBAAA,CAAmCM,gBAAgB,GAAAD,qBAAA;IAAAI,qBAAA,GAEnER,OAAO,CADVS,qBAAqB;IAAEC,yBAAyB,GAAAF,qBAAA,cAAGC,qBAAqB,GAAAD,qBAAA;EAEzE,IAAI,OAAOH,gBAAgB,KAAK,UAAU,EAAE;IAC3C,MAAM,IAAIM,SAAS,CAClB,mEACD,CAAC;EACF;EACA;EACA,IAAIN,gBAAgB,CAACP,OAAO,CAAC,CAACc,UAAU,KAAK,QAAQ,EAAE;IACtD,OAAO,IAAI;EACZ;EAEA,IAAIC,cAA8B,GAAGf,OAAO;EAC5C,OAAOe,cAAc,EAAE;IACtB,IAAIH,yBAAyB,CAACG,cAAc,EAAE;MAAER,gBAAgB,EAAhBA;IAAiB,CAAC,CAAC,EAAE;MACpE,OAAO,IAAI;IACZ;IAEAQ,cAAc,GAAGA,cAAc,CAACC,aAAa;EAC9C;EAEA,OAAO,KAAK;AACb;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASL,qBAAqBA,CACpCX,OAAgB,EAEN;EAAA,IAAAiB,sBAAA;EAAA,IADVf,OAAqC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAE1C,IAAAe,sBAAA,GAEIhB,OAAO,CADVK,gBAAgB;IAAhBA,gBAAgB,GAAAW,sBAAA,eAAAD,sBAAA,GAAGjB,OAAO,CAACQ,aAAa,CAACC,WAAW,cAAAQ,sBAAA,uBAAjCA,sBAAA,CAAmCV,gBAAgB,GAAAW,sBAAA;EAEvE,IAAI,OAAOX,gBAAgB,KAAK,UAAU,EAAE;IAC3C,MAAM,IAAIM,SAAS,CAClB,mEACD,CAAC;EACF;EAEA,IAAKb,OAAO,CAAiBmB,MAAM,KAAK,IAAI,EAAE;IAC7C,OAAO,IAAI;EACZ;EAEA,IAAInB,OAAO,CAACoB,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;IACnD,OAAO,IAAI;EACZ;EAEA,IAAIb,gBAAgB,CAACP,OAAO,CAAC,CAACqB,OAAO,KAAK,MAAM,EAAE;IACjD,OAAO,IAAI;EACZ;EAEA,OAAO,KAAK;AACb"}