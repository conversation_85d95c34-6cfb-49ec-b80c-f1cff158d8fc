import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
});

export const metadata: Metadata = {
  title: "EliteStore - Premium Fashion Collection",
  description: "Discover our exclusive collection of premium clothing designed for the modern lifestyle. Quality, style, and comfort in every piece.",
  keywords: ["fashion", "clothing", "premium", "style", "modern", "quality"],
  authors: [{ name: "EliteStore" }],
  creator: "EliteStore",
  publisher: "EliteStore",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://elitestore.com"),
  alternates: {
    canonical: "/",
    languages: {
      "en-US": "/en-US",
      "tr-TR": "/tr-TR",
      "de-DE": "/de-DE",
    },
  },
  openGraph: {
    title: "EliteStore - Premium Fashion Collection",
    description: "Discover our exclusive collection of premium clothing designed for the modern lifestyle.",
    url: "https://elitestore.com",
    siteName: "EliteStore",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "EliteStore - Premium Fashion Collection",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "EliteStore - Premium Fashion Collection",
    description: "Discover our exclusive collection of premium clothing designed for the modern lifestyle.",
    images: ["/og-image.jpg"],
    creator: "@elitestore",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#FFD700" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
