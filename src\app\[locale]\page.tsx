'use client'

import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { ProductGrid } from '@/components/products/ProductGrid'
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher'
import { useCart } from '@/contexts/CartContext'
import { useTranslations } from 'next-intl'
import { ShoppingBag, Star, Truck, Shield, ArrowRight, Search, Menu, User, Heart } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Product, Category } from '@/types'

// Mock data for featured products
const mockCategories: Category[] = [
  { id: '1', name: "Men's Clothing", slug: 'mens-clothing', description: '', order: 1, isActive: true },
  { id: '2', name: "Women's Clothing", slug: 'womens-clothing', description: '', order: 2, isActive: true },
  { id: '3', name: 'Accessories', slug: 'accessories', description: '', order: 3, isActive: true },
]

const featuredProducts: Product[] = [
  {
    id: '1',
    name: 'Premium Cotton T-Shirt',
    description: 'High-quality cotton t-shirt with perfect fit and comfort.',
    price: 49.99,
    sku: 'MEN-TSHIRT-001',
    slug: 'premium-cotton-tshirt',
    images: [
      { id: '1', url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500', alt: 'Premium Cotton T-Shirt', isPrimary: true, order: 1 }
    ],
    category: mockCategories[0],
    variants: [],
    tags: ['cotton', 'casual'],
    inStock: true,
    stockQuantity: 50,
    featured: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    name: 'Classic Denim Jeans',
    description: 'Timeless denim jeans crafted from premium denim fabric.',
    price: 89.99,
    salePrice: 69.99,
    sku: 'MEN-JEANS-001',
    slug: 'classic-denim-jeans',
    images: [
      { id: '2', url: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500', alt: 'Classic Denim Jeans', isPrimary: true, order: 1 }
    ],
    category: mockCategories[0],
    variants: [],
    tags: ['denim', 'classic'],
    inStock: true,
    stockQuantity: 30,
    featured: true,
    createdAt: '2024-01-02',
    updatedAt: '2024-01-02',
  },
  {
    id: '3',
    name: 'Elegant Silk Blouse',
    description: 'Beautiful silk blouse with elegant draping.',
    price: 119.99,
    salePrice: 99.99,
    sku: 'WOM-BLOUSE-001',
    slug: 'elegant-silk-blouse',
    images: [
      { id: '3', url: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500', alt: 'Elegant Silk Blouse', isPrimary: true, order: 1 }
    ],
    category: mockCategories[1],
    variants: [],
    tags: ['silk', 'elegant'],
    inStock: true,
    stockQuantity: 20,
    featured: true,
    createdAt: '2024-01-03',
    updatedAt: '2024-01-03',
  },
  {
    id: '4',
    name: 'Leather Crossbody Bag',
    description: 'Stylish leather crossbody bag with multiple compartments.',
    price: 149.99,
    salePrice: 129.99,
    sku: 'ACC-BAG-001',
    slug: 'leather-crossbody-bag',
    images: [
      { id: '4', url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=500', alt: 'Leather Crossbody Bag', isPrimary: true, order: 1 }
    ],
    category: mockCategories[2],
    variants: [],
    tags: ['leather', 'bag'],
    inStock: true,
    stockQuantity: 18,
    featured: true,
    createdAt: '2024-01-04',
    updatedAt: '2024-01-04',
  },
]

export default function Home() {
  const { state, toggleCart, addItem } = useCart()
  const t = useTranslations()

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-primary">
                  Elite<span className="text-accent">Store</span>
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link href="/" className="text-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  {t('navigation.home')}
                </Link>
                <Link href="/products?category=mens-clothing" className="text-muted-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  {t('navigation.men')}
                </Link>
                <Link href="/products?category=womens-clothing" className="text-muted-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  {t('navigation.women')}
                </Link>
                <Link href="/products?category=accessories" className="text-muted-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  {t('navigation.accessories')}
                </Link>
                <Link href="/products?featured=true" className="text-muted-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  {t('navigation.sale')}
                </Link>
              </div>
            </nav>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              <div className="hidden md:block">
                <div className="relative">
                  <Input
                    type="search"
                    placeholder={t('navigation.search_placeholder')}
                    className="w-64 pl-10"
                    leftIcon={<Search className="h-4 w-4" />}
                  />
                </div>
              </div>
              
              <LanguageSwitcher />
              
              <Button variant="ghost" size="icon">
                <User className="h-5 w-5" />
              </Button>
              
              <Button variant="ghost" size="icon">
                <Heart className="h-5 w-5" />
              </Button>
              
              <Button variant="ghost" size="icon" className="relative" onClick={toggleCart}>
                <ShoppingBag className="h-5 w-5" />
                {state.itemCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-accent text-primary text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                    {state.itemCount}
                  </span>
                )}
              </Button>
              
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 to-white py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-primary mb-6">
                {t('hero.title')}
                <span className="block text-accent">{t('hero.subtitle')}</span>
              </h1>
              <p className="text-lg text-muted-foreground mb-8 max-w-lg">
                {t('hero.description')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/products">
                  <Button size="lg" className="text-base px-8">
                    {t('hero.shop_now')}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/products">
                  <Button variant="outline" size="lg" className="text-base px-8">
                    {t('hero.view_collection')}
                  </Button>
                </Link>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative"
            >
              <div className="aspect-square bg-gradient-to-br from-accent/20 to-accent/5 rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <div className="w-32 h-32 bg-accent rounded-full flex items-center justify-center mb-4 mx-auto">
                    <ShoppingBag className="h-16 w-16 text-primary" />
                  </div>
                  <p className="text-lg font-medium text-primary">Premium Collection</p>
                  <p className="text-muted-foreground">Coming Soon</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <CardContent className="pt-6">
                  <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <Truck className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl mb-2">{t('features.free_shipping.title')}</CardTitle>
                  <CardDescription>
                    {t('features.free_shipping.description')}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <CardContent className="pt-6">
                  <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl mb-2">{t('features.quality_guarantee.title')}</CardTitle>
                  <CardDescription>
                    {t('features.quality_guarantee.description')}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <CardContent className="pt-6">
                  <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <Star className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl mb-2">{t('features.premium_service.title')}</CardTitle>
                  <CardDescription>
                    {t('features.premium_service.description')}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">{t('products.featured_title')}</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {t('products.featured_subtitle')}
            </p>
          </div>

          <ProductGrid
            products={featuredProducts}
            columns={4}
            onQuickView={(product) => {
              console.log('Quick view:', product)
            }}
            onAddToCart={(product) => {
              addItem(product)
            }}
            onToggleWishlist={(product) => {
              console.log('Toggle wishlist:', product)
            }}
          />

          <div className="text-center mt-12">
            <Link href="/products">
              <Button variant="outline" size="lg">
                {t('products.view_all')}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-primary text-secondary">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">{t('newsletter.title')}</h2>
            <p className="text-lg mb-8 text-gray-200">
              {t('newsletter.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder={t('newsletter.email_placeholder')}
                className="flex-1 bg-secondary text-primary"
              />
              <Button variant="accent" size="lg">
                {t('newsletter.subscribe')}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">
                Elite<span className="text-accent">Store</span>
              </h3>
              <p className="text-gray-400 mb-4">
                {t('footer.description')}
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">{t('footer.shop.title')}</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/products?category=mens-clothing" className="hover:text-accent transition-colors">{t('footer.shop.mens_clothing')}</Link></li>
                <li><Link href="/products?category=womens-clothing" className="hover:text-accent transition-colors">{t('footer.shop.womens_clothing')}</Link></li>
                <li><Link href="/products?category=accessories" className="hover:text-accent transition-colors">{t('footer.shop.accessories')}</Link></li>
                <li><Link href="/products?featured=true" className="hover:text-accent transition-colors">{t('footer.shop.sale_items')}</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">{t('footer.support.title')}</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-accent transition-colors">{t('footer.support.contact_us')}</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">{t('footer.support.size_guide')}</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">{t('footer.support.shipping_info')}</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">{t('footer.support.returns')}</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">{t('footer.company.title')}</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-accent transition-colors">{t('footer.company.about_us')}</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">{t('footer.company.careers')}</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">{t('footer.company.privacy_policy')}</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">{t('footer.company.terms_of_service')}</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>{t('footer.copyright')}</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
