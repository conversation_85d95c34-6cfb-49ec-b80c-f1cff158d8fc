{"version": 3, "sources": [], "sections": [{"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/config.ts"], "sourcesContent": ["import { notFound } from 'next/navigation'\nimport { getRequestConfig } from 'next-intl/server'\n\n// Can be imported from a shared config\nexport const locales = ['en', 'tr', 'de'] as const\nexport type Locale = typeof locales[number]\n\nexport const defaultLocale: Locale = 'en'\n\nexport const localeNames: Record<Locale, string> = {\n  en: 'English',\n  tr: 'Türkçe',\n  de: 'Deutsch',\n}\n\nexport const localeFlags: Record<Locale, string> = {\n  en: '🇺🇸',\n  tr: '🇹🇷',\n  de: '🇩🇪',\n}\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) notFound()\n\n  return {\n    messages: (await import(`./messages/${locale}.json`)).default\n  }\n})\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;;;AAGO,MAAM,UAAU;IAAC;IAAM;IAAM;CAAK;AAGlC,MAAM,gBAAwB;AAE9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;uCAEe,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAmB,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD;IAEhD,OAAO;QACL,UAAU,CAAC;;;;;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF"}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport { locales, defaultLocale } from './i18n/config';\n\nexport default createMiddleware({\n  // A list of all locales that are supported\n  locales,\n\n  // Used when no locale matches\n  defaultLocale,\n\n  // Don't use locale prefix for default locale\n  localePrefix: 'as-needed'\n});\n\nexport const config = {\n  // Match only internationalized pathnames\n  matcher: ['/((?!api|_next|_vercel|.*\\\\..*).*)']\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE;IAC9B,2CAA2C;IAC3C,SAAA,6HAAA,CAAA,UAAO;IAEP,8BAA8B;IAC9B,eAAA,6HAAA,CAAA,gBAAa;IAEb,6CAA6C;IAC7C,cAAc;AAChB;AAEO,MAAM,SAAS;IACpB,yCAAyC;IACzC,SAAS;QAAC;KAAqC;AACjD"}}]}