{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'elevated' | 'outlined' | 'ghost'\n  padding?: 'none' | 'sm' | 'md' | 'lg'\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'default', padding = 'md', children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-lg border bg-card text-card-foreground transition-all duration-200',\n          {\n            'border-border shadow-sm hover:shadow-md': variant === 'default',\n            'border-border shadow-lg hover:shadow-xl': variant === 'elevated',\n            'border-2 border-border shadow-none': variant === 'outlined',\n            'border-none shadow-none bg-transparent': variant === 'ghost',\n          },\n          {\n            'p-0': padding === 'none',\n            'p-3': padding === 'sm',\n            'p-6': padding === 'md',\n            'p-8': padding === 'lg',\n          },\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\nCard.displayName = 'Card'\n\nconst CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 pb-6', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, children, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-2xl font-semibold leading-none tracking-tight text-foreground', className)}\n      {...props}\n    >\n      {children}\n    </h3>\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center pt-6', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAOA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,UAAU,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvE,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8EACA;YACE,2CAA2C,YAAY;YACvD,2CAA2C,YAAY;YACvD,sCAAsC,YAAY;YAClD,0CAA0C,YAAY;QACxD,GACA;YACE,OAAO,YAAY;YACnB,OAAO,YAAY;YACnB,OAAO,YAAY;YACnB,OAAO,YAAY;QACrB,GACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAEF,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAC/C,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAChC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;QACnF,GAAG,KAAK;kBAER;;;;;;AAIP,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAG9D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/app/%5Blocale%5D/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  TrendingUp, \n  TrendingDown, \n  DollarSign, \n  ShoppingCart, \n  Users, \n  Package,\n  Eye,\n  MoreHorizontal\n} from 'lucide-react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { formatPrice } from '@/lib/utils'\n\n// Mock data for dashboard\nconst stats = [\n  {\n    title: 'Total Revenue',\n    value: '$45,231.89',\n    change: '+20.1%',\n    trend: 'up',\n    icon: DollarSign,\n  },\n  {\n    title: 'Orders',\n    value: '2,350',\n    change: '+180.1%',\n    trend: 'up',\n    icon: ShoppingCart,\n  },\n  {\n    title: 'Customers',\n    value: '1,234',\n    change: '+19%',\n    trend: 'up',\n    icon: Users,\n  },\n  {\n    title: 'Products',\n    value: '573',\n    change: '+201',\n    trend: 'up',\n    icon: Package,\n  },\n]\n\nconst recentOrders = [\n  {\n    id: 'ORD-001',\n    customer: '<PERSON>',\n    email: '<EMAIL>',\n    amount: 299.99,\n    status: 'completed',\n    date: '2024-01-15',\n  },\n  {\n    id: 'ORD-002',\n    customer: '<PERSON>',\n    email: '<EMAIL>',\n    amount: 159.99,\n    status: 'processing',\n    date: '2024-01-15',\n  },\n  {\n    id: 'ORD-003',\n    customer: 'Mike Johnson',\n    email: '<EMAIL>',\n    amount: 89.99,\n    status: 'shipped',\n    date: '2024-01-14',\n  },\n  {\n    id: 'ORD-004',\n    customer: 'Sarah Wilson',\n    email: '<EMAIL>',\n    amount: 199.99,\n    status: 'pending',\n    date: '2024-01-14',\n  },\n]\n\nconst topProducts = [\n  {\n    id: '1',\n    name: 'Premium Cotton T-Shirt',\n    sales: 145,\n    revenue: 7225,\n    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=100',\n  },\n  {\n    id: '2',\n    name: 'Classic Denim Jeans',\n    sales: 89,\n    revenue: 6231,\n    image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=100',\n  },\n  {\n    id: '3',\n    name: 'Elegant Silk Blouse',\n    sales: 67,\n    revenue: 8033,\n    image: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=100',\n  },\n  {\n    id: '4',\n    name: 'Leather Crossbody Bag',\n    sales: 54,\n    revenue: 8099,\n    image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100',\n  },\n]\n\nexport default function AdminDashboard() {\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n        <p className=\"text-gray-600 mt-2\">\n          Welcome back! Here's what's happening with your store today.\n        </p>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat, index) => (\n          <motion.div\n            key={stat.title}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: index * 0.1 }}\n          >\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium text-gray-600\">\n                  {stat.title}\n                </CardTitle>\n                <stat.icon className=\"h-4 w-4 text-gray-600\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-gray-900\">{stat.value}</div>\n                <div className=\"flex items-center text-xs text-gray-600 mt-1\">\n                  {stat.trend === 'up' ? (\n                    <TrendingUp className=\"h-3 w-3 text-green-500 mr-1\" />\n                  ) : (\n                    <TrendingDown className=\"h-3 w-3 text-red-500 mr-1\" />\n                  )}\n                  <span className={stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}>\n                    {stat.change}\n                  </span>\n                  <span className=\"ml-1\">from last month</span>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        ))}\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Recent Orders */}\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>Recent Orders</CardTitle>\n              <CardDescription>\n                Latest orders from your customers\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentOrders.map((order) => (\n                  <div key={order.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center justify-between\">\n                        <p className=\"font-medium text-gray-900\">{order.customer}</p>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          order.status === 'completed' ? 'bg-green-100 text-green-800' :\n                          order.status === 'processing' ? 'bg-blue-100 text-blue-800' :\n                          order.status === 'shipped' ? 'bg-purple-100 text-purple-800' :\n                          'bg-yellow-100 text-yellow-800'\n                        }`}>\n                          {order.status}\n                        </span>\n                      </div>\n                      <p className=\"text-sm text-gray-600\">{order.email}</p>\n                      <div className=\"flex items-center justify-between mt-1\">\n                        <p className=\"text-sm font-medium\">{formatPrice(order.amount)}</p>\n                        <p className=\"text-xs text-gray-500\">{order.date}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <div className=\"mt-4\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  <Eye className=\"h-4 w-4 mr-2\" />\n                  View All Orders\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* Top Products */}\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: 0.5 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>Top Products</CardTitle>\n              <CardDescription>\n                Best performing products this month\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {topProducts.map((product) => (\n                  <div key={product.id} className=\"flex items-center gap-4\">\n                    <div className=\"w-12 h-12 bg-gray-100 rounded-lg overflow-hidden\">\n                      <img\n                        src={product.image}\n                        alt={product.name}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"font-medium text-gray-900 truncate\">{product.name}</p>\n                      <p className=\"text-sm text-gray-600\">{product.sales} sales</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-medium text-gray-900\">{formatPrice(product.revenue)}</p>\n                      <p className=\"text-xs text-gray-500\">revenue</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <div className=\"mt-4\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  <Eye className=\"h-4 w-4 mr-2\" />\n                  View All Products\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n\n      {/* Quick Actions */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5, delay: 0.6 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle>Quick Actions</CardTitle>\n            <CardDescription>\n              Common tasks to manage your store\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <Button className=\"h-20 flex-col gap-2\">\n                <Package className=\"h-6 w-6\" />\n                Add New Product\n              </Button>\n              <Button variant=\"outline\" className=\"h-20 flex-col gap-2\">\n                <ShoppingCart className=\"h-6 w-6\" />\n                Process Orders\n              </Button>\n              <Button variant=\"outline\" className=\"h-20 flex-col gap-2\">\n                <Users className=\"h-6 w-6\" />\n                Manage Customers\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAhBA;;;;;;;AAkBA,0BAA0B;AAC1B,MAAM,QAAQ;IACZ;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,kNAAA,CAAA,aAAU;IAClB;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,sNAAA,CAAA,eAAY;IACpB;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,wMAAA,CAAA,UAAO;IACf;CACD;AAED,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;CACD;AAED,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;kCAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,KAAK,KAAK;;;;;;sDAEb,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;;8CAEvB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAoC,KAAK,KAAK;;;;;;sDAC7D,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,KAAK,KAAK,qBACd,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;yEAEtB,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DAE1B,8OAAC;oDAAK,WAAW,KAAK,KAAK,KAAK,OAAO,mBAAmB;8DACvD,KAAK,MAAM;;;;;;8DAEd,8OAAC;oDAAK,WAAU;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;uBAvBxB,KAAK,KAAK;;;;;;;;;;0BA+BrB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;oDAAmB,WAAU;8DAC5B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAA6B,MAAM,QAAQ;;;;;;kFACxD,8OAAC;wEAAK,WAAW,CAAC,+BAA+B,EAC/C,MAAM,MAAM,KAAK,cAAc,gCAC/B,MAAM,MAAM,KAAK,eAAe,8BAChC,MAAM,MAAM,KAAK,YAAY,kCAC7B,iCACA;kFACC,MAAM,MAAM;;;;;;;;;;;;0EAGjB,8OAAC;gEAAE,WAAU;0EAAyB,MAAM,KAAK;;;;;;0EACjD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAuB,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,MAAM;;;;;;kFAC5D,8OAAC;wEAAE,WAAU;kFAAyB,MAAM,IAAI;;;;;;;;;;;;;;;;;;mDAhB5C,MAAM,EAAE;;;;;;;;;;sDAsBtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,wBAChB,8OAAC;oDAAqB,WAAU;;sEAC9B,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,KAAK,QAAQ,KAAK;gEAClB,KAAK,QAAQ,IAAI;gEACjB,WAAU;;;;;;;;;;;sEAGd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAsC,QAAQ,IAAI;;;;;;8EAC/D,8OAAC;oEAAE,WAAU;;wEAAyB,QAAQ,KAAK;wEAAC;;;;;;;;;;;;;sEAEtD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAA6B,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,OAAO;;;;;;8EACrE,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;mDAd/B,QAAQ,EAAE;;;;;;;;;;sDAmBxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGjC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGtC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7C", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "file": "trending-down.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/node_modules/lucide-react/src/icons/trending-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 17h6v-6', key: 't6n2it' }],\n  ['path', { d: 'm22 17-8.5-8.5-5 5L2 7', key: 'x473p' }],\n];\n\n/**\n * @component @name TrendingDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTdoNnYtNiIgLz4KICA8cGF0aCBkPSJtMjIgMTctOC41LTguNS01IDVMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trending-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingDown = createLucideIcon('trending-down', __iconNode);\n\nexport default TrendingDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "file": "dollar-sign.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "file": "eye.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}