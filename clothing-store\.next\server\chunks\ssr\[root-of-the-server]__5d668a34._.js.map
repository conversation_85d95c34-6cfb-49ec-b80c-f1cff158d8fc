{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react'\nimport { Product, ProductVariant } from '@/types'\n\nexport interface CartItem {\n  id: string\n  product: Product\n  variant?: ProductVariant\n  quantity: number\n  price: number\n  addedAt: string\n}\n\nexport interface CartState {\n  items: CartItem[]\n  subtotal: number\n  tax: number\n  shipping: number\n  total: number\n  itemCount: number\n  isOpen: boolean\n}\n\ntype CartAction =\n  | { type: 'ADD_ITEM'; payload: { product: Product; variant?: ProductVariant; quantity?: number } }\n  | { type: 'REMOVE_ITEM'; payload: { id: string } }\n  | { type: 'UPDATE_QUANTITY'; payload: { id: string; quantity: number } }\n  | { type: 'CLEAR_CART' }\n  | { type: 'TOGGLE_CART' }\n  | { type: 'OPEN_CART' }\n  | { type: 'CLOSE_CART' }\n  | { type: 'LOAD_CART'; payload: CartItem[] }\n\nconst TAX_RATE = 0.08 // 8% tax\nconst FREE_SHIPPING_THRESHOLD = 100\nconst SHIPPING_COST = 10\n\nfunction calculateTotals(items: CartItem[]) {\n  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)\n  const tax = subtotal * TAX_RATE\n  const shipping = subtotal >= FREE_SHIPPING_THRESHOLD ? 0 : SHIPPING_COST\n  const total = subtotal + tax + shipping\n  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0)\n\n  return { subtotal, tax, shipping, total, itemCount }\n}\n\nfunction cartReducer(state: CartState, action: CartAction): CartState {\n  switch (action.type) {\n    case 'ADD_ITEM': {\n      const { product, variant, quantity = 1 } = action.payload\n      const price = product.salePrice || product.price\n      const itemId = `${product.id}-${variant?.id || 'default'}`\n      \n      const existingItemIndex = state.items.findIndex(item => item.id === itemId)\n      \n      let newItems: CartItem[]\n      \n      if (existingItemIndex >= 0) {\n        // Update existing item quantity\n        newItems = state.items.map((item, index) =>\n          index === existingItemIndex\n            ? { ...item, quantity: item.quantity + quantity }\n            : item\n        )\n      } else {\n        // Add new item\n        const newItem: CartItem = {\n          id: itemId,\n          product,\n          variant,\n          quantity,\n          price,\n          addedAt: new Date().toISOString(),\n        }\n        newItems = [...state.items, newItem]\n      }\n      \n      const totals = calculateTotals(newItems)\n      \n      return {\n        ...state,\n        items: newItems,\n        ...totals,\n      }\n    }\n    \n    case 'REMOVE_ITEM': {\n      const newItems = state.items.filter(item => item.id !== action.payload.id)\n      const totals = calculateTotals(newItems)\n      \n      return {\n        ...state,\n        items: newItems,\n        ...totals,\n      }\n    }\n    \n    case 'UPDATE_QUANTITY': {\n      const { id, quantity } = action.payload\n      \n      if (quantity <= 0) {\n        return cartReducer(state, { type: 'REMOVE_ITEM', payload: { id } })\n      }\n      \n      const newItems = state.items.map(item =>\n        item.id === id ? { ...item, quantity } : item\n      )\n      const totals = calculateTotals(newItems)\n      \n      return {\n        ...state,\n        items: newItems,\n        ...totals,\n      }\n    }\n    \n    case 'CLEAR_CART': {\n      return {\n        ...state,\n        items: [],\n        subtotal: 0,\n        tax: 0,\n        shipping: 0,\n        total: 0,\n        itemCount: 0,\n      }\n    }\n    \n    case 'TOGGLE_CART': {\n      return {\n        ...state,\n        isOpen: !state.isOpen,\n      }\n    }\n    \n    case 'OPEN_CART': {\n      return {\n        ...state,\n        isOpen: true,\n      }\n    }\n    \n    case 'CLOSE_CART': {\n      return {\n        ...state,\n        isOpen: false,\n      }\n    }\n    \n    case 'LOAD_CART': {\n      const totals = calculateTotals(action.payload)\n      return {\n        ...state,\n        items: action.payload,\n        ...totals,\n      }\n    }\n    \n    default:\n      return state\n  }\n}\n\nconst initialState: CartState = {\n  items: [],\n  subtotal: 0,\n  tax: 0,\n  shipping: 0,\n  total: 0,\n  itemCount: 0,\n  isOpen: false,\n}\n\ninterface CartContextType {\n  state: CartState\n  addItem: (product: Product, variant?: ProductVariant, quantity?: number) => void\n  removeItem: (id: string) => void\n  updateQuantity: (id: string, quantity: number) => void\n  clearCart: () => void\n  toggleCart: () => void\n  openCart: () => void\n  closeCart: () => void\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined)\n\nexport function CartProvider({ children }: { children: React.ReactNode }) {\n  const [state, dispatch] = useReducer(cartReducer, initialState)\n\n  // Load cart from localStorage on mount\n  useEffect(() => {\n    const savedCart = localStorage.getItem('cart')\n    if (savedCart) {\n      try {\n        const cartItems = JSON.parse(savedCart)\n        dispatch({ type: 'LOAD_CART', payload: cartItems })\n      } catch (error) {\n        console.error('Error loading cart from localStorage:', error)\n      }\n    }\n  }, [])\n\n  // Save cart to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('cart', JSON.stringify(state.items))\n  }, [state.items])\n\n  const addItem = (product: Product, variant?: ProductVariant, quantity = 1) => {\n    dispatch({ type: 'ADD_ITEM', payload: { product, variant, quantity } })\n  }\n\n  const removeItem = (id: string) => {\n    dispatch({ type: 'REMOVE_ITEM', payload: { id } })\n  }\n\n  const updateQuantity = (id: string, quantity: number) => {\n    dispatch({ type: 'UPDATE_QUANTITY', payload: { id, quantity } })\n  }\n\n  const clearCart = () => {\n    dispatch({ type: 'CLEAR_CART' })\n  }\n\n  const toggleCart = () => {\n    dispatch({ type: 'TOGGLE_CART' })\n  }\n\n  const openCart = () => {\n    dispatch({ type: 'OPEN_CART' })\n  }\n\n  const closeCart = () => {\n    dispatch({ type: 'CLOSE_CART' })\n  }\n\n  return (\n    <CartContext.Provider\n      value={{\n        state,\n        addItem,\n        removeItem,\n        updateQuantity,\n        clearCart,\n        toggleCart,\n        openCart,\n        closeCart,\n      }}\n    >\n      {children}\n    </CartContext.Provider>\n  )\n}\n\nexport function useCart() {\n  const context = useContext(CartContext)\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAkCA,MAAM,WAAW,KAAK,SAAS;;AAC/B,MAAM,0BAA0B;AAChC,MAAM,gBAAgB;AAEtB,SAAS,gBAAgB,KAAiB;IACxC,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAG;IACjF,MAAM,MAAM,WAAW;IACvB,MAAM,WAAW,YAAY,0BAA0B,IAAI;IAC3D,MAAM,QAAQ,WAAW,MAAM;IAC/B,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;IAEnE,OAAO;QAAE;QAAU;QAAK;QAAU;QAAO;IAAU;AACrD;AAEA,SAAS,YAAY,KAAgB,EAAE,MAAkB;IACvD,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAY;gBACf,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE,GAAG,OAAO,OAAO;gBACzD,MAAM,QAAQ,QAAQ,SAAS,IAAI,QAAQ,KAAK;gBAChD,MAAM,SAAS,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,SAAS,MAAM,WAAW;gBAE1D,MAAM,oBAAoB,MAAM,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAEpE,IAAI;gBAEJ,IAAI,qBAAqB,GAAG;oBAC1B,gCAAgC;oBAChC,WAAW,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAChC,UAAU,oBACN;4BAAE,GAAG,IAAI;4BAAE,UAAU,KAAK,QAAQ,GAAG;wBAAS,IAC9C;gBAER,OAAO;oBACL,eAAe;oBACf,MAAM,UAAoB;wBACxB,IAAI;wBACJ;wBACA;wBACA;wBACA;wBACA,SAAS,IAAI,OAAO,WAAW;oBACjC;oBACA,WAAW;2BAAI,MAAM,KAAK;wBAAE;qBAAQ;gBACtC;gBAEA,MAAM,SAAS,gBAAgB;gBAE/B,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP,GAAG,MAAM;gBACX;YACF;QAEA,KAAK;YAAe;gBAClB,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;gBACzE,MAAM,SAAS,gBAAgB;gBAE/B,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP,GAAG,MAAM;gBACX;YACF;QAEA,KAAK;YAAmB;gBACtB,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;gBAEvC,IAAI,YAAY,GAAG;oBACjB,OAAO,YAAY,OAAO;wBAAE,MAAM;wBAAe,SAAS;4BAAE;wBAAG;oBAAE;gBACnE;gBAEA,MAAM,WAAW,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OAC/B,KAAK,EAAE,KAAK,KAAK;wBAAE,GAAG,IAAI;wBAAE;oBAAS,IAAI;gBAE3C,MAAM,SAAS,gBAAgB;gBAE/B,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP,GAAG,MAAM;gBACX;YACF;QAEA,KAAK;YAAc;gBACjB,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO,EAAE;oBACT,UAAU;oBACV,KAAK;oBACL,UAAU;oBACV,OAAO;oBACP,WAAW;gBACb;YACF;QAEA,KAAK;YAAe;gBAClB,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,CAAC,MAAM,MAAM;gBACvB;YACF;QAEA,KAAK;YAAa;gBAChB,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ;gBACV;YACF;QAEA,KAAK;YAAc;gBACjB,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ;gBACV;YACF;QAEA,KAAK;YAAa;gBAChB,MAAM,SAAS,gBAAgB,OAAO,OAAO;gBAC7C,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO,OAAO,OAAO;oBACrB,GAAG,MAAM;gBACX;YACF;QAEA;YACE,OAAO;IACX;AACF;AAEA,MAAM,eAA0B;IAC9B,OAAO,EAAE;IACT,UAAU;IACV,KAAK;IACL,UAAU;IACV,OAAO;IACP,WAAW;IACX,QAAQ;AACV;AAaA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,IAAI;gBACF,MAAM,YAAY,KAAK,KAAK,CAAC;gBAC7B,SAAS;oBAAE,MAAM;oBAAa,SAAS;gBAAU;YACnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,MAAM,KAAK;IACzD,GAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,MAAM,UAAU,CAAC,SAAkB,SAA0B,WAAW,CAAC;QACvE,SAAS;YAAE,MAAM;YAAY,SAAS;gBAAE;gBAAS;gBAAS;YAAS;QAAE;IACvE;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe,SAAS;gBAAE;YAAG;QAAE;IAClD;IAEA,MAAM,iBAAiB,CAAC,IAAY;QAClC,SAAS;YAAE,MAAM;YAAmB,SAAS;gBAAE;gBAAI;YAAS;QAAE;IAChE;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;QAAa;IAChC;IAEA,MAAM,aAAa;QACjB,SAAS;YAAE,MAAM;QAAc;IACjC;IAEA,MAAM,WAAW;QACf,SAAS;YAAE,MAAM;QAAY;IAC/B;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;QAAa;IAChC;IAEA,qBACE,8OAAC,YAAY,QAAQ;QACnB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(price)\n}\n\nexport function formatPriceMultiCurrency(price: number, locale: string = 'en-US', currency: string = 'USD'): string {\n  return new Intl.NumberFormat(locale, {\n    style: 'currency',\n    currency,\n  }).format(price)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function capitalizeFirst(text: string): string {\n  return text.charAt(0).toUpperCase() + text.slice(1)\n}\n\nexport function getImageUrl(path: string, width?: number, height?: number): string {\n  if (!path) return '/placeholder-image.jpg'\n  \n  // If it's already a full URL, return as is\n  if (path.startsWith('http')) return path\n  \n  // Add optimization parameters if provided\n  let url = path.startsWith('/') ? path : `/${path}`\n  \n  if (width || height) {\n    const params = new URLSearchParams()\n    if (width) params.set('w', width.toString())\n    if (height) params.set('h', height.toString())\n    url += `?${params.toString()}`\n  }\n  \n  return url\n}\n\nexport function calculateDiscountPercentage(originalPrice: number, salePrice: number): number {\n  if (originalPrice <= 0 || salePrice >= originalPrice) return 0\n  return Math.round(((originalPrice - salePrice) / originalPrice) * 100)\n}\n\nexport function formatDate(date: Date | string, locale: string = 'en-US'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  return dateObj.toLocaleDateString(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport function getRelativeTime(date: Date | string, locale: string = 'en-US'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n  \n  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' })\n  \n  if (diffInSeconds < 60) return rtf.format(-diffInSeconds, 'second')\n  if (diffInSeconds < 3600) return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')\n  if (diffInSeconds < 86400) return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')\n  if (diffInSeconds < 2592000) return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')\n  if (diffInSeconds < 31536000) return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')\n  return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year')\n}\n\nexport function parseSearchParams(searchParams: URLSearchParams) {\n  const params: Record<string, string | string[]> = {}\n  \n  for (const [key, value] of searchParams.entries()) {\n    if (params[key]) {\n      if (Array.isArray(params[key])) {\n        (params[key] as string[]).push(value)\n      } else {\n        params[key] = [params[key] as string, value]\n      }\n    } else {\n      params[key] = value\n    }\n  }\n  \n  return params\n}\n\nexport function createSearchParams(params: Record<string, string | string[] | undefined>): string {\n  const searchParams = new URLSearchParams()\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined) {\n      if (Array.isArray(value)) {\n        value.forEach(v => searchParams.append(key, v))\n      } else {\n        searchParams.set(key, value)\n      }\n    }\n  })\n  \n  return searchParams.toString()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAmB,KAAK;IACjE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,yBAAyB,KAAa,EAAE,SAAiB,OAAO,EAAE,WAAmB,KAAK;IACxG,OAAO,IAAI,KAAK,YAAY,CAAC,QAAQ;QACnC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;AACnD;AAEO,SAAS,YAAY,IAAY,EAAE,KAAc,EAAE,MAAe;IACvE,IAAI,CAAC,MAAM,OAAO;IAElB,2CAA2C;IAC3C,IAAI,KAAK,UAAU,CAAC,SAAS,OAAO;IAEpC,0CAA0C;IAC1C,IAAI,MAAM,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;IAElD,IAAI,SAAS,QAAQ;QACnB,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,OAAO,GAAG,CAAC,KAAK,MAAM,QAAQ;QACzC,IAAI,QAAQ,OAAO,GAAG,CAAC,KAAK,OAAO,QAAQ;QAC3C,OAAO,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAChC;IAEA,OAAO;AACT;AAEO,SAAS,4BAA4B,aAAqB,EAAE,SAAiB;IAClF,IAAI,iBAAiB,KAAK,aAAa,eAAe,OAAO;IAC7D,OAAO,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,SAAS,IAAI,gBAAiB;AACpE;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAiB,OAAO;IACtE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,gBAAgB,IAAmB,EAAE,SAAiB,OAAO;IAC3E,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,MAAM,MAAM,IAAI,KAAK,kBAAkB,CAAC,QAAQ;QAAE,SAAS;IAAO;IAElE,IAAI,gBAAgB,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,eAAe;IAC1D,IAAI,gBAAgB,MAAM,OAAO,IAAI,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,gBAAgB,KAAK;IAC7E,IAAI,gBAAgB,OAAO,OAAO,IAAI,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,gBAAgB,OAAO;IAChF,IAAI,gBAAgB,SAAS,OAAO,IAAI,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,gBAAgB,QAAQ;IACnF,IAAI,gBAAgB,UAAU,OAAO,IAAI,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,gBAAgB,UAAU;IACtF,OAAO,IAAI,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,gBAAgB,WAAW;AAC3D;AAEO,SAAS,kBAAkB,YAA6B;IAC7D,MAAM,SAA4C,CAAC;IAEnD,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,aAAa,OAAO,GAAI;QACjD,IAAI,MAAM,CAAC,IAAI,EAAE;YACf,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG;gBAC7B,MAAM,CAAC,IAAI,CAAc,IAAI,CAAC;YACjC,OAAO;gBACL,MAAM,CAAC,IAAI,GAAG;oBAAC,MAAM,CAAC,IAAI;oBAAY;iBAAM;YAC9C;QACF,OAAO;YACL,MAAM,CAAC,IAAI,GAAG;QAChB;IACF;IAEA,OAAO;AACT;AAEO,SAAS,mBAAmB,MAAqD;IACtF,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,WAAW;YACvB,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,aAAa,MAAM,CAAC,KAAK;YAC9C,OAAO;gBACL,aAAa,GAAG,CAAC,KAAK;YACxB;QACF;IACF;IAEA,OAAO,aAAa,QAAQ;AAC9B", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\nimport { Loader2 } from 'lucide-react'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95',\n  {\n    variants: {\n      variant: {\n        primary: 'bg-primary text-secondary hover:bg-gray-800 shadow-md hover:shadow-lg',\n        secondary: 'bg-secondary text-primary border border-border hover:bg-gray-50 shadow-sm hover:shadow-md',\n        accent: 'bg-accent text-primary hover:bg-accent-dark shadow-md hover:shadow-lg font-semibold',\n        outline: 'border border-primary text-primary hover:bg-primary hover:text-secondary',\n        ghost: 'text-primary hover:bg-gray-100 hover:text-gray-900',\n        destructive: 'bg-error text-white hover:bg-red-600 shadow-md hover:shadow-lg',\n        link: 'text-primary underline-offset-4 hover:underline p-0 h-auto',\n      },\n      size: {\n        sm: 'h-8 px-3 text-xs',\n        md: 'h-10 px-4 py-2',\n        lg: 'h-12 px-6 text-base',\n        xl: 'h-14 px-8 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'primary',\n      size: 'md',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n        {!loading && leftIcon && <span className=\"mr-2\">{leftIcon}</span>}\n        {children}\n        {!loading && rightIcon && <span className=\"ml-2\">{rightIcon}</span>}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,6QACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,QAAQ;YACR,SAAS;YACT,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBAAW,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAC9B,CAAC,WAAW,0BAAY,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD;YACA,CAAC,WAAW,2BAAa,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/NewShop/clothing-store/src/components/cart/CartSidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { X, Plus, Minus, ShoppingBag, Trash2 } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { useCart } from '@/contexts/CartContext'\nimport { formatPrice } from '@/lib/utils'\nimport { cn } from '@/lib/utils'\n\nexport function CartSidebar() {\n  const { state, removeItem, updateQuantity, closeCart } = useCart()\n\n  return (\n    <AnimatePresence>\n      {state.isOpen && (\n        <>\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 z-50\"\n            onClick={closeCart}\n          />\n\n          {/* Sidebar */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: 'spring', damping: 30, stiffness: 300 }}\n            className=\"fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-xl z-50 flex flex-col\"\n          >\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 border-b\">\n              <h2 className=\"text-lg font-semibold text-primary\">\n                Shopping Cart ({state.itemCount})\n              </h2>\n              <Button variant=\"ghost\" size=\"icon\" onClick={closeCart}>\n                <X className=\"h-5 w-5\" />\n              </Button>\n            </div>\n\n            {/* Cart Items */}\n            <div className=\"flex-1 overflow-y-auto\">\n              {state.items.length === 0 ? (\n                <div className=\"flex flex-col items-center justify-center h-full p-8 text-center\">\n                  <ShoppingBag className=\"h-16 w-16 text-gray-300 mb-4\" />\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Your cart is empty</h3>\n                  <p className=\"text-gray-500 mb-6\">Add some products to get started</p>\n                  <Button onClick={closeCart}>\n                    Continue Shopping\n                  </Button>\n                </div>\n              ) : (\n                <div className=\"p-4 space-y-4\">\n                  {state.items.map((item) => (\n                    <CartItem\n                      key={item.id}\n                      item={item}\n                      onUpdateQuantity={updateQuantity}\n                      onRemove={removeItem}\n                    />\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Footer */}\n            {state.items.length > 0 && (\n              <div className=\"border-t bg-gray-50 p-4 space-y-4\">\n                {/* Totals */}\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span>Subtotal</span>\n                    <span>{formatPrice(state.subtotal)}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span>Tax</span>\n                    <span>{formatPrice(state.tax)}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span>Shipping</span>\n                    <span>\n                      {state.shipping === 0 ? (\n                        <span className=\"text-green-600\">Free</span>\n                      ) : (\n                        formatPrice(state.shipping)\n                      )}\n                    </span>\n                  </div>\n                  {state.subtotal < 100 && state.subtotal > 0 && (\n                    <div className=\"text-xs text-gray-600\">\n                      Add {formatPrice(100 - state.subtotal)} more for free shipping\n                    </div>\n                  )}\n                  <div className=\"flex justify-between font-semibold text-lg border-t pt-2\">\n                    <span>Total</span>\n                    <span>{formatPrice(state.total)}</span>\n                  </div>\n                </div>\n\n                {/* Actions */}\n                <div className=\"space-y-2\">\n                  <Link href=\"/checkout\" onClick={closeCart}>\n                    <Button className=\"w-full\" size=\"lg\">\n                      Checkout\n                    </Button>\n                  </Link>\n                  <Link href=\"/cart\" onClick={closeCart}>\n                    <Button variant=\"outline\" className=\"w-full\">\n                      View Cart\n                    </Button>\n                  </Link>\n                </div>\n              </div>\n            )}\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  )\n}\n\ninterface CartItemProps {\n  item: any\n  onUpdateQuantity: (id: string, quantity: number) => void\n  onRemove: (id: string) => void\n}\n\nfunction CartItem({ item, onUpdateQuantity, onRemove }: CartItemProps) {\n  const primaryImage = item.product.images?.find((img: any) => img.isPrimary) || item.product.images?.[0]\n\n  return (\n    <div className=\"flex gap-3 p-3 bg-white rounded-lg border\">\n      {/* Product Image */}\n      <div className=\"flex-shrink-0\">\n        <div className=\"w-16 h-16 bg-gray-100 rounded-md overflow-hidden\">\n          {primaryImage ? (\n            <Image\n              src={primaryImage.url}\n              alt={primaryImage.alt || item.product.name}\n              width={64}\n              height={64}\n              className=\"w-full h-full object-cover\"\n            />\n          ) : (\n            <div className=\"w-full h-full flex items-center justify-center\">\n              <ShoppingBag className=\"h-6 w-6 text-gray-400\" />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Product Details */}\n      <div className=\"flex-1 min-w-0\">\n        <h4 className=\"text-sm font-medium text-gray-900 truncate\">\n          {item.product.name}\n        </h4>\n        <p className=\"text-xs text-gray-500 mt-1\">\n          {item.product.category.name}\n        </p>\n        {item.variant && (\n          <p className=\"text-xs text-gray-500\">\n            {item.variant.type}: {item.variant.value}\n          </p>\n        )}\n        <div className=\"flex items-center justify-between mt-2\">\n          <span className=\"text-sm font-medium text-primary\">\n            {formatPrice(item.price)}\n          </span>\n          <div className=\"flex items-center gap-2\">\n            {/* Quantity Controls */}\n            <div className=\"flex items-center border rounded-md\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-6 w-6\"\n                onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}\n                disabled={item.quantity <= 1}\n              >\n                <Minus className=\"h-3 w-3\" />\n              </Button>\n              <span className=\"px-2 text-sm font-medium min-w-[2rem] text-center\">\n                {item.quantity}\n              </span>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-6 w-6\"\n                onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}\n              >\n                <Plus className=\"h-3 w-3\" />\n              </Button>\n            </div>\n            \n            {/* Remove Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"h-6 w-6 text-red-500 hover:text-red-700\"\n              onClick={() => onRemove(item.id)}\n            >\n              <Trash2 className=\"h-3 w-3\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE/D,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,MAAM,MAAM,kBACX;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAqC;wCACjC,MAAM,SAAS;wCAAC;;;;;;;8CAElC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS;8CAC3C,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK,CAAC,MAAM,KAAK,kBACtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;kDAAW;;;;;;;;;;;qDAK9B,8OAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC;wCAEC,MAAM;wCACN,kBAAkB;wCAClB,UAAU;uCAHL,KAAK,EAAE;;;;;;;;;;;;;;;wBAWrB,MAAM,KAAK,CAAC,MAAM,GAAG,mBACpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAM,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,QAAQ;;;;;;;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAM,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,GAAG;;;;;;;;;;;;sDAE9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DACE,MAAM,QAAQ,KAAK,kBAClB,8OAAC;wDAAK,WAAU;kEAAiB;;;;;+DAEjC,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,QAAQ;;;;;;;;;;;;wCAI/B,MAAM,QAAQ,GAAG,OAAO,MAAM,QAAQ,GAAG,mBACxC,8OAAC;4CAAI,WAAU;;gDAAwB;gDAChC,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,MAAM,QAAQ;gDAAE;;;;;;;sDAG3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAM,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK;;;;;;;;;;;;;;;;;;8CAKlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,SAAS;sDAC9B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;gDAAS,MAAK;0DAAK;;;;;;;;;;;sDAIvC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,SAAS;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjE;AAQA,SAAS,SAAS,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAiB;IACnE,MAAM,eAAe,KAAK,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,MAAa,IAAI,SAAS,KAAK,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;IAEvG,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,6BACC,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,aAAa,GAAG;wBACrB,KAAK,aAAa,GAAG,IAAI,KAAK,OAAO,CAAC,IAAI;wBAC1C,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;6CAGZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,KAAK,OAAO,CAAC,IAAI;;;;;;kCAEpB,8OAAC;wBAAE,WAAU;kCACV,KAAK,OAAO,CAAC,QAAQ,CAAC,IAAI;;;;;;oBAE5B,KAAK,OAAO,kBACX,8OAAC;wBAAE,WAAU;;4BACV,KAAK,OAAO,CAAC,IAAI;4BAAC;4BAAG,KAAK,OAAO,CAAC,KAAK;;;;;;;kCAG5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK;;;;;;0CAEzB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;gDACzD,UAAU,KAAK,QAAQ,IAAI;0DAE3B,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAK,WAAU;0DACb,KAAK,QAAQ;;;;;;0DAEhB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;0DAEzD,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKpB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,SAAS,KAAK,EAAE;kDAE/B,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC", "debugId": null}}]}