'use client'

import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { ProductGrid } from '@/components/products/ProductGrid'
import { useCart } from '@/contexts/CartContext'
import { ShoppingBag, Star, Truck, Shield, ArrowRight, Search, Menu, User, Heart } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Product, Category } from '@/types'

// Mock data for featured products
const mockCategories: Category[] = [
  { id: '1', name: "Men's Clothing", slug: 'mens-clothing', description: '', order: 1, isActive: true },
  { id: '2', name: "Women's Clothing", slug: 'womens-clothing', description: '', order: 2, isActive: true },
  { id: '3', name: 'Accessories', slug: 'accessories', description: '', order: 3, isActive: true },
]

const featuredProducts: Product[] = [
  {
    id: '1',
    name: 'Premium Cotton T-Shirt',
    description: 'High-quality cotton t-shirt with perfect fit and comfort.',
    price: 49.99,
    sku: 'MEN-TSHIRT-001',
    slug: 'premium-cotton-tshirt',
    images: [
      { id: '1', url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500', alt: 'Premium Cotton T-Shirt', isPrimary: true, order: 1 }
    ],
    category: mockCategories[0],
    variants: [],
    tags: ['cotton', 'casual'],
    inStock: true,
    stockQuantity: 50,
    featured: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    name: 'Classic Denim Jeans',
    description: 'Timeless denim jeans crafted from premium denim fabric.',
    price: 89.99,
    salePrice: 69.99,
    sku: 'MEN-JEANS-001',
    slug: 'classic-denim-jeans',
    images: [
      { id: '2', url: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500', alt: 'Classic Denim Jeans', isPrimary: true, order: 1 }
    ],
    category: mockCategories[0],
    variants: [],
    tags: ['denim', 'classic'],
    inStock: true,
    stockQuantity: 30,
    featured: true,
    createdAt: '2024-01-02',
    updatedAt: '2024-01-02',
  },
  {
    id: '3',
    name: 'Elegant Silk Blouse',
    description: 'Beautiful silk blouse with elegant draping.',
    price: 119.99,
    salePrice: 99.99,
    sku: 'WOM-BLOUSE-001',
    slug: 'elegant-silk-blouse',
    images: [
      { id: '3', url: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500', alt: 'Elegant Silk Blouse', isPrimary: true, order: 1 }
    ],
    category: mockCategories[1],
    variants: [],
    tags: ['silk', 'elegant'],
    inStock: true,
    stockQuantity: 20,
    featured: true,
    createdAt: '2024-01-03',
    updatedAt: '2024-01-03',
  },
  {
    id: '4',
    name: 'Leather Crossbody Bag',
    description: 'Stylish leather crossbody bag with multiple compartments.',
    price: 149.99,
    salePrice: 129.99,
    sku: 'ACC-BAG-001',
    slug: 'leather-crossbody-bag',
    images: [
      { id: '4', url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=500', alt: 'Leather Crossbody Bag', isPrimary: true, order: 1 }
    ],
    category: mockCategories[2],
    variants: [],
    tags: ['leather', 'bag'],
    inStock: true,
    stockQuantity: 18,
    featured: true,
    createdAt: '2024-01-04',
    updatedAt: '2024-01-04',
  },
]

export default function Home() {
  const { state, toggleCart, addItem } = useCart()

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-primary">
                  Elite<span className="text-accent">Store</span>
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link href="/" className="text-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  Home
                </Link>
                <Link href="/products?category=mens-clothing" className="text-muted-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  Men
                </Link>
                <Link href="/products?category=womens-clothing" className="text-muted-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  Women
                </Link>
                <Link href="/products?category=accessories" className="text-muted-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  Accessories
                </Link>
                <Link href="/products?featured=true" className="text-muted-foreground hover:text-accent transition-colors px-3 py-2 text-sm font-medium">
                  Sale
                </Link>
              </div>
            </nav>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              <div className="hidden md:block">
                <div className="relative">
                  <Input
                    type="search"
                    placeholder="Search products..."
                    className="w-64 pl-10"
                    leftIcon={<Search className="h-4 w-4" />}
                  />
                </div>
              </div>

              <Button variant="ghost" size="icon">
                <User className="h-5 w-5" />
              </Button>

              <Button variant="ghost" size="icon">
                <Heart className="h-5 w-5" />
              </Button>

              <Button variant="ghost" size="icon" className="relative" onClick={toggleCart}>
                <ShoppingBag className="h-5 w-5" />
                {state.itemCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-accent text-primary text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                    {state.itemCount}
                  </span>
                )}
              </Button>

              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 to-white py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-primary mb-6">
                Premium Fashion
                <span className="block text-accent">Collection</span>
              </h1>
              <p className="text-lg text-muted-foreground mb-8 max-w-lg">
                Discover our exclusive collection of premium clothing designed for the modern lifestyle.
                Quality, style, and comfort in every piece.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/products">
                  <Button size="lg" className="text-base px-8">
                    Shop Now
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/products">
                  <Button variant="outline" size="lg" className="text-base px-8">
                    View Collection
                  </Button>
                </Link>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative"
            >
              <div className="aspect-square bg-gradient-to-br from-accent/20 to-accent/5 rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <div className="w-32 h-32 bg-accent rounded-full flex items-center justify-center mb-4 mx-auto">
                    <ShoppingBag className="h-16 w-16 text-primary" />
                  </div>
                  <p className="text-lg font-medium text-primary">Premium Collection</p>
                  <p className="text-muted-foreground">Coming Soon</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <CardContent className="pt-6">
                  <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <Truck className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl mb-2">Free Shipping</CardTitle>
                  <CardDescription>
                    Free shipping on all orders over $100. Fast and reliable delivery worldwide.
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <CardContent className="pt-6">
                  <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl mb-2">Quality Guarantee</CardTitle>
                  <CardDescription>
                    Premium materials and craftsmanship. 30-day return policy on all items.
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <CardContent className="pt-6">
                  <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                    <Star className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl mb-2">Premium Service</CardTitle>
                  <CardDescription>
                    24/7 customer support and personalized styling advice from our experts.
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">Featured Products</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our handpicked selection of premium clothing items
            </p>
          </div>

          <ProductGrid
            products={featuredProducts}
            columns={4}
            onQuickView={(product) => {
              console.log('Quick view:', product)
            }}
            onAddToCart={(product) => {
              addItem(product)
            }}
            onToggleWishlist={(product) => {
              console.log('Toggle wishlist:', product)
            }}
          />

          <div className="text-center mt-12">
            <Link href="/products">
              <Button variant="outline" size="lg">
                View All Products
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-primary text-secondary">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
            <p className="text-lg mb-8 text-gray-200">
              Subscribe to our newsletter and be the first to know about new collections and exclusive offers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                className="flex-1 bg-secondary text-primary"
              />
              <Button variant="accent" size="lg">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">
                Elite<span className="text-accent">Store</span>
              </h3>
              <p className="text-gray-400 mb-4">
                Premium fashion for the modern lifestyle. Quality, style, and comfort in every piece.
              </p>
              <div className="flex space-x-4">
                <Button variant="ghost" size="icon" className="text-gray-400 hover:text-accent">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </Button>
                <Button variant="ghost" size="icon" className="text-gray-400 hover:text-accent">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-**********-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .***********.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </Button>
                <Button variant="ghost" size="icon" className="text-gray-400 hover:text-accent">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.**************.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                  </svg>
                </Button>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Shop</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/products?category=mens-clothing" className="hover:text-accent transition-colors">Men's Clothing</Link></li>
                <li><Link href="/products?category=womens-clothing" className="hover:text-accent transition-colors">Women's Clothing</Link></li>
                <li><Link href="/products?category=accessories" className="hover:text-accent transition-colors">Accessories</Link></li>
                <li><Link href="/products?featured=true" className="hover:text-accent transition-colors">Sale Items</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-accent transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Size Guide</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Shipping Info</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Returns</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-accent transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 EliteStore. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
