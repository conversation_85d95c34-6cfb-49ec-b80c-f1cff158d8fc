import { test, expect } from '@playwright/test'

test.describe('Shopping Cart', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/en')
    // Wait for products to load
    await page.waitForSelector('[data-testid="product-grid"]', { timeout: 10000 })
  })

  test('should add product to cart', async ({ page }) => {
    // Find the first product's add to cart button
    const firstProduct = page.locator('[data-testid="product-card"]').first()
    const addToCartButton = firstProduct.getByRole('button').filter({ has: page.locator('svg') }).first()
    
    // Click add to cart
    await addToCartButton.click()
    
    // Check if cart badge appears with count
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2)
    await expect(cartButton.locator('span')).toBeVisible()
    await expect(cartButton.locator('span')).toHaveText('1')
  })

  test('should open cart sidebar', async ({ page }) => {
    // Add a product first
    const firstProduct = page.locator('[data-testid="product-card"]').first()
    const addToCartButton = firstProduct.getByRole('button').filter({ has: page.locator('svg') }).first()
    await addToCartButton.click()
    
    // Click cart button to open sidebar
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2)
    await cartButton.click()
    
    // Check if cart sidebar is visible
    await expect(page.getByText(/Shopping Cart/i)).toBeVisible()
    await expect(page.getByText(/Premium Cotton T-Shirt/i)).toBeVisible()
  })

  test('should update quantity in cart', async ({ page }) => {
    // Add a product first
    const firstProduct = page.locator('[data-testid="product-card"]').first()
    const addToCartButton = firstProduct.getByRole('button').filter({ has: page.locator('svg') }).first()
    await addToCartButton.click()
    
    // Open cart sidebar
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2)
    await cartButton.click()
    
    // Find quantity controls
    const increaseButton = page.getByRole('button', { name: '+' })
    await increaseButton.click()
    
    // Check if quantity updated
    await expect(cartButton.locator('span')).toHaveText('2')
  })

  test('should remove item from cart', async ({ page }) => {
    // Add a product first
    const firstProduct = page.locator('[data-testid="product-card"]').first()
    const addToCartButton = firstProduct.getByRole('button').filter({ has: page.locator('svg') }).first()
    await addToCartButton.click()
    
    // Open cart sidebar
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2)
    await cartButton.click()
    
    // Find and click remove button
    const removeButton = page.getByRole('button').filter({ has: page.locator('svg') }).last()
    await removeButton.click()
    
    // Check if cart is empty
    await expect(page.getByText(/Your cart is empty/i)).toBeVisible()
    await expect(cartButton.locator('span')).not.toBeVisible()
  })

  test('should calculate total correctly', async ({ page }) => {
    // Add multiple products
    const products = page.locator('[data-testid="product-card"]')
    
    // Add first product (Premium Cotton T-Shirt - $49.99)
    await products.nth(0).getByRole('button').filter({ has: page.locator('svg') }).first().click()
    
    // Add second product (Classic Denim Jeans - $69.99 sale price)
    await products.nth(1).getByRole('button').filter({ has: page.locator('svg') }).first().click()
    
    // Open cart sidebar
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2)
    await cartButton.click()
    
    // Check total calculation
    await expect(page.getByText(/\$119\.98/)).toBeVisible() // $49.99 + $69.99
  })

  test('should persist cart items on page reload', async ({ page }) => {
    // Add a product
    const firstProduct = page.locator('[data-testid="product-card"]').first()
    const addToCartButton = firstProduct.getByRole('button').filter({ has: page.locator('svg') }).first()
    await addToCartButton.click()
    
    // Reload page
    await page.reload()
    await page.waitForSelector('[data-testid="product-grid"]', { timeout: 10000 })
    
    // Check if cart still has items
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2)
    await expect(cartButton.locator('span')).toBeVisible()
    await expect(cartButton.locator('span')).toHaveText('1')
  })

  test('should navigate to cart page', async ({ page }) => {
    // Add a product first
    const firstProduct = page.locator('[data-testid="product-card"]').first()
    const addToCartButton = firstProduct.getByRole('button').filter({ has: page.locator('svg') }).first()
    await addToCartButton.click()
    
    // Open cart sidebar
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2)
    await cartButton.click()
    
    // Click "View Cart" button
    const viewCartButton = page.getByRole('button', { name: /View Cart/i })
    await viewCartButton.click()
    
    // Check if navigated to cart page
    await expect(page).toHaveURL(/\/cart/)
    await expect(page.getByRole('heading', { name: /Shopping Cart/i })).toBeVisible()
  })

  test('should handle empty cart state', async ({ page }) => {
    // Open cart sidebar without adding items
    const cartButton = page.getByRole('button').filter({ has: page.locator('svg') }).nth(2)
    await cartButton.click()
    
    // Check empty cart message
    await expect(page.getByText(/Your cart is empty/i)).toBeVisible()
    await expect(page.getByText(/Start shopping to add items/i)).toBeVisible()
  })
})
